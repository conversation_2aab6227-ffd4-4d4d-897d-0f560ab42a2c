'use client'

import { useState, useCallback } from 'react'
import { useAuth } from './useAuth'

interface UpdateProfileData {
  name: string
  email: string
}

interface UseUserProfileReturn {
  user: any
  isLoading: boolean
  isAuthenticated: boolean
  isRefreshing: boolean
  updateProfile: (data: UpdateProfileData) => Promise<{ success: boolean; user?: any; error?: string }>
  refreshUser: () => Promise<any>
  deleteAccount: () => Promise<{ success: boolean; error?: string }>
  lastRefresh: Date | null
}

export function useUserProfile(): UseUserProfileReturn {
  const { user, isLoading, isAuthenticated, refreshUser, updateProfile: authUpdateProfile } = useAuth()
  const [isDeleting, setIsDeleting] = useState(false)

  // Enhanced update profile with better error handling
  const updateProfile = useCallback(async (data: UpdateProfileData): Promise<{ success: boolean; user?: any; error?: string }> => {
    // Validate input
    if (!data.name?.trim() || !data.email?.trim()) {
      return { success: false, error: 'Name and email are required' }
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      return { success: false, error: 'Please enter a valid email address' }
    }

    try {
      const result = await authUpdateProfile({
        name: data.name.trim(),
        email: data.email.trim()
      })

      return result
    } catch (error) {
      console.error('Profile update error:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }, [authUpdateProfile])

  // Delete account function
  const deleteAccount = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    if (!user?.id) {
      return { success: false, error: 'Not authenticated' }
    }

    try {
      setIsDeleting(true)

      const response = await fetch('/api/user/profile', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok && result.success) {
        return { success: true }
      } else {
        return { success: false, error: result.error || 'Failed to delete account' }
      }
    } catch (error) {
      console.error('Account deletion error:', error)
      return { success: false, error: 'Network error occurred' }
    } finally {
      setIsDeleting(false)
    }
  }, [user?.id])

  return {
    user,
    isLoading: isLoading || isDeleting,
    isAuthenticated,
    isRefreshing: false, // Not available from useAuth
    updateProfile,
    refreshUser,
    deleteAccount,
    lastRefresh: null, // Not available from useAuth
  }
}
