#!/usr/bin/env node

/**
 * Cleanup cron job script
 *
 * This script should be run daily to:
 * 1. Clean up expired pending apps
 * 2. Send expiry warnings
 * 3. Clean up orphaned files
 *
 * Add to crontab:
 * 0 2 * * * /usr/bin/node /path/to/your/app/src/scripts/cleanup-cron.ts
 */

import { cleanupService } from '../lib/cleanup'
import { prisma } from '../lib/prisma'
import { formatFileSize } from '../lib/formatUtils'

async function runCleanup() {
  console.log(`[${new Date().toISOString()}] Starting cleanup process...`)

  try {
    // 1. Send expiry warnings first
    console.log('Sending expiry warnings...')
    const warningsSent = await cleanupService.sendExpiryWarnings()
    console.log(`✓ Sent ${warningsSent} expiry warnings`)

    // 2. Clean up expired pending apps
    console.log('Cleaning up expired pending apps...')
    const expiredResult = await cleanupService.cleanupExpiredPendingApps()
    console.log(`✓ Deleted ${expiredResult.deletedApps} expired apps`)
    console.log(`✓ Deleted ${expiredResult.deletedFiles} app files`)

    if (expiredResult.errors.length > 0) {
      console.log('Errors during expired apps cleanup:')
      expiredResult.errors.forEach(error => console.log(`  ✗ ${error}`))
    }

    // 3. Clean up orphaned files
    console.log('Cleaning up orphaned files...')
    const orphanedResult = await cleanupService.cleanupOrphanedFiles()
    console.log(`✓ Deleted ${orphanedResult.deletedFiles} orphaned files`)

    if (orphanedResult.errors.length > 0) {
      console.log('Errors during orphaned files cleanup:')
      orphanedResult.errors.forEach(error => console.log(`  ✗ ${error}`))
    }

    // 4. Get final stats
    const stats = await cleanupService.getCleanupStats()
    console.log('\nCleanup Summary:')
    console.log(`  Pending apps: ${stats.pendingAppsCount}`)
    console.log(`  Apps nearing expiry: ${stats.appsNearingExpiry}`)
    console.log(`  Expired apps: ${stats.expiredAppsCount}`)
    console.log(`  Total storage: ${formatFileSize(stats.totalStorageSize)}`)

    console.log(`[${new Date().toISOString()}] Cleanup process completed successfully`)

  } catch (error) {
    console.error(`[${new Date().toISOString()}] Cleanup process failed:`, error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...')
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...')
  await prisma.$disconnect()
  process.exit(0)
})

// Run the cleanup
runCleanup()
  .catch(console.error)
  .finally(() => prisma.$disconnect())
