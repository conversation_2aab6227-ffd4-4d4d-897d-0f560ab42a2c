import { prisma } from '../lib/prisma'

const emailTemplates = [
  {
    name: 'APP_CREATED',
    subject: 'App Submitted Successfully - {{appName}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #333; margin-bottom: 20px;">App Submitted Successfully! 🎉</h1>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Hi {{userName}},
          </p>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Your app <strong>{{appName}}</strong> has been successfully submitted to AveHub and is now pending review.
          </p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">What happens next?</h3>
            <ul style="color: #666; line-height: 1.6;">
              <li>Our team will review your app within 5 business days</li>
              <li>You'll receive an email notification once the review is complete</li>
              <li>If approved, your app will be published to the AveHub store</li>
              <li>If changes are needed, we'll provide detailed feedback</li>
            </ul>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            You can track your app's status anytime in your developer dashboard.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Dashboard
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            Best regards,<br>
            The AveHub Team
          </p>
        </div>
      </div>
    `,
    textContent: `
      Hi {{userName}},
      
      Your app {{appName}} has been successfully submitted to AveHub and is now pending review.
      
      What happens next?
      - Our team will review your app within 5 business days
      - You'll receive an email notification once the review is complete
      - If approved, your app will be published to the AveHub store
      - If changes are needed, we'll provide detailed feedback
      
      You can track your app's status anytime in your developer dashboard: {{dashboardUrl}}
      
      Best regards,
      The AveHub Team
    `,
    variables: ['userName', 'appName', 'dashboardUrl']
  },
  {
    name: 'APP_APPROVED',
    subject: 'Congratulations! {{appName}} has been approved',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #28a745; margin-bottom: 20px;">🎉 App Approved!</h1>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Hi {{userName}},
          </p>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Great news! Your app <strong>{{appName}}</strong> has been approved and is now live on AveHub!
          </p>
          
          <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h3 style="color: #155724; margin-top: 0;">Your app is now available to users!</h3>
            <p style="color: #155724; margin-bottom: 0;">
              Users can now discover, download, and enjoy your app through the AveHub platform.
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{appUrl}}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-right: 10px;">
              View Your App
            </a>
            <a href="{{dashboardUrl}}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Dashboard
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            Best regards,<br>
            The AveHub Team
          </p>
        </div>
      </div>
    `,
    textContent: `
      Hi {{userName}},
      
      Great news! Your app {{appName}} has been approved and is now live on AveHub!
      
      Your app is now available to users who can discover, download, and enjoy it through the AveHub platform.
      
      View your app: {{appUrl}}
      Dashboard: {{dashboardUrl}}
      
      Best regards,
      The AveHub Team
    `,
    variables: ['userName', 'appName', 'appUrl', 'dashboardUrl']
  },
  {
    name: 'APP_REJECTED',
    subject: 'App Review Update - {{appName}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #dc3545; margin-bottom: 20px;">App Review Update</h1>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Hi {{userName}},
          </p>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Thank you for submitting <strong>{{appName}}</strong> to AveHub. After careful review, we need some changes before we can approve your app.
          </p>
          
          <div style="background-color: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;">
            <h3 style="color: #721c24; margin-top: 0;">Feedback:</h3>
            <p style="color: #721c24; margin-bottom: 0;">
              {{reason}}
            </p>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Please address these issues and resubmit your app. Our team is here to help you succeed!
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Update Your App
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            Best regards,<br>
            The AveHub Team
          </p>
        </div>
      </div>
    `,
    textContent: `
      Hi {{userName}},
      
      Thank you for submitting {{appName}} to AveHub. After careful review, we need some changes before we can approve your app.
      
      Feedback: {{reason}}
      
      Please address these issues and resubmit your app. Our team is here to help you succeed!
      
      Update your app: {{dashboardUrl}}
      
      Best regards,
      The AveHub Team
    `,
    variables: ['userName', 'appName', 'reason', 'dashboardUrl']
  },
  {
    name: 'PENDING_EXPIRY_WARNING',
    subject: 'Action Required: {{appName}} expires soon',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #ffc107; margin-bottom: 20px;">⚠️ Action Required</h1>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Hi {{userName}},
          </p>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Your app <strong>{{appName}}</strong> has been pending review and will expire on <strong>{{expiryDate}}</strong>.
          </p>
          
          <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="color: #856404; margin-top: 0;">What you need to do:</h3>
            <ul style="color: #856404; line-height: 1.6;">
              <li>Review your app submission for any missing information</li>
              <li>Ensure all required files are uploaded</li>
              <li>Check that your app meets our guidelines</li>
              <li>Contact support if you need assistance</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #ffc107; color: #212529; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Review Your App
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            Best regards,<br>
            The AveHub Team
          </p>
        </div>
      </div>
    `,
    textContent: `
      Hi {{userName}},
      
      Your app {{appName}} has been pending review and will expire on {{expiryDate}}.
      
      What you need to do:
      - Review your app submission for any missing information
      - Ensure all required files are uploaded
      - Check that your app meets our guidelines
      - Contact support if you need assistance
      
      Review your app: {{dashboardUrl}}
      
      Best regards,
      The AveHub Team
    `,
    variables: ['userName', 'appName', 'expiryDate', 'dashboardUrl']
  }
]

async function seedEmailTemplates() {
  console.log('Seeding email templates...')
  
  for (const template of emailTemplates) {
    try {
      await prisma.emailTemplate.upsert({
        where: { name: template.name },
        update: template,
        create: template
      })
      console.log(`✓ Created/updated template: ${template.name}`)
    } catch (error) {
      console.error(`✗ Failed to create template ${template.name}:`, error)
    }
  }
  
  console.log('Email templates seeding completed!')
}

// Run if called directly
if (require.main === module) {
  seedEmailTemplates()
    .catch(console.error)
    .finally(() => prisma.$disconnect())
}

export { seedEmailTemplates }
