import { SUPPORTED_FILE_TYPES, FileValidationResult, FileTypeConfig } from '@/types/fileTypes'

/**
 * Optimized file validation without virus scanning
 * Validates file based on extension, MIME type, and magic numbers
 */
export async function validateFile(file: File): Promise<FileValidationResult> {
  const result: FileValidationResult = {
    isValid: false,
    errors: [],
    warnings: [],
    size: file.size
  }

  try {
    // Get file extension
    const extension = getFileExtension(file.name).toLowerCase()
    result.detectedExtension = extension

    // Get MIME type
    result.detectedMimeType = file.type

    // Find matching file type configuration
    const fileTypeConfig = findFileTypeConfig(extension, file.type)
    
    if (!fileTypeConfig) {
      result.errors.push(`Unsupported file type: ${extension || file.type}`)
      return result
    }

    result.fileType = Object.keys(SUPPORTED_FILE_TYPES).find(
      key => SUPPORTED_FILE_TYPES[key] === fileTypeConfig
    )
    result.category = fileTypeConfig.category

    // Validate file size (reduced to 100MB for free storage optimization)
    const maxSize = Math.min(fileTypeConfig.maxSize, 100 * 1024 * 1024) // 100MB max
    if (file.size > maxSize) {
      result.errors.push(
        `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`
      )
    }

    // Validate magic numbers (file signature)
    const magicNumberValid = await validateMagicNumbers(file, fileTypeConfig.magicNumbers)
    if (!magicNumberValid) {
      result.warnings.push('File signature does not match expected format')
    }

    // Basic security checks (simplified)
    await performSecurityChecks(file, result)

    // File is valid if no errors
    result.isValid = result.errors.length === 0

    return result

  } catch (error) {
    result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    return result
  }
}

/**
 * Extract file extension from filename
 */
function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.')
  return lastDot !== -1 ? filename.substring(lastDot) : ''
}

/**
 * Find file type configuration based on extension and MIME type
 */
function findFileTypeConfig(extension: string, mimeType: string): FileTypeConfig | null {
  for (const config of Object.values(SUPPORTED_FILE_TYPES)) {
    const extensionMatch = config.extensions.includes(extension)
    const mimeTypeMatch = config.mimeTypes.includes(mimeType)
    
    if (extensionMatch || mimeTypeMatch) {
      return config
    }
  }
  return null
}

/**
 * Validate file magic numbers (file signature)
 */
async function validateMagicNumbers(file: File, expectedMagicNumbers: string[]): Promise<boolean> {
  if (expectedMagicNumbers.length === 0) return true

  try {
    // Read first 32 bytes of the file
    const arrayBuffer = await file.slice(0, 32).arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)
    
    // Convert to hex string
    const hexString = Array.from(uint8Array)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('')
      .toUpperCase()

    // Check if any expected magic number matches
    return expectedMagicNumbers.some(magic => 
      hexString.startsWith(magic.toUpperCase())
    )
  } catch (error) {
    console.error('Error validating magic numbers:', error)
    return false
  }
}

/**
 * Perform additional security checks
 */
async function performSecurityChecks(file: File, result: FileValidationResult): Promise<void> {
  // Check for suspicious file patterns (simplified)
  const suspiciousPatterns = [
    /\.(bat|cmd|exe|scr|pif|com)$/i,
    /\.(vbs|js|jar|ps1|sh)$/i
  ]

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.name)) {
      result.warnings.push('File type may be potentially unsafe')
      break
    }
  }

  // Check for double extensions
  const doubleExtensionPattern = /\.[a-zA-Z0-9]{1,4}\.[a-zA-Z0-9]{1,4}$/
  if (doubleExtensionPattern.test(file.name)) {
    result.warnings.push('File has double extension which may be suspicious')
  }

  // Check for very long filenames (potential buffer overflow)
  if (file.name.length > 255) {
    result.errors.push('Filename is too long')
  }

  // Check for null bytes in filename
  if (file.name.includes('\0')) {
    result.errors.push('Filename contains null bytes')
  }

  // Check for hidden characters or unicode tricks
  if (/[\u200B-\u200D\uFEFF]/g.test(file.name)) {
    result.warnings.push('Filename contains hidden unicode characters')
  }
}

/**
 * Format file size in human readable format
 */
function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`
}

/**
 * Get all supported file extensions
 */
export function getSupportedExtensions(): string[] {
  const extensions: string[] = []
  Object.values(SUPPORTED_FILE_TYPES).forEach(config => {
    extensions.push(...config.extensions)
  })
  return extensions
}

/**
 * Get supported file types by category
 */
export function getSupportedFileTypesByCategory(category: string): FileTypeConfig[] {
  return Object.values(SUPPORTED_FILE_TYPES).filter(config => config.category === category)
}

/**
 * Check if file type is supported
 */
export function isFileTypeSupported(extension: string, mimeType?: string): boolean {
  return findFileTypeConfig(extension.toLowerCase(), mimeType || '') !== null
}
