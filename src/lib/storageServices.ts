/**
 * Alternative Storage Services Configuration
 * Free storage platforms with different capabilities
 */

export interface StorageService {
  name: string
  freeStorage: string
  maxFileSize: string
  creditCardRequired: boolean
  pricing: string
  bestFor: string[]
  setupInstructions: string
  pros: string[]
  cons: string[]
}

export const STORAGE_SERVICES: StorageService[] = [
  {
    name: "Netlify Blobs",
    freeStorage: "2GB",
    maxFileSize: "100MB per file",
    creditCardRequired: false,
    pricing: "Free forever, $20/month for more",
    bestFor: ["Web apps", "Static files", "Easy integration"],
    setupInstructions: "Already configured - using Netlify Blobs",
    pros: [
      "2GB free storage forever",
      "Simple API",
      "Global CDN included",
      "No credit card required",
      "Easy to use"
    ],
    cons: [
      "100MB file size limit",
      "Tied to Netlify ecosystem"
    ]
  },
  {
    name: "Vercel Blob",
    freeStorage: "1GB",
    maxFileSize: "4.5MB per file",
    creditCardRequired: false,
    pricing: "$20/month for 100GB",
    bestFor: ["Next.js apps", "Vercel deployment", "Small files"],
    setupInstructions: `
1. Install @vercel/blob package
2. Add to .env:
   BLOB_READ_WRITE_TOKEN=your_token
    `,
    pros: [
      "1GB free storage",
      "Perfect for Next.js",
      "Global edge network",
      "Zero config on Vercel",
      "No credit card required"
    ],
    cons: [
      "4.5MB file size limit",
      "Smaller free tier",
      "Vercel ecosystem only"
    ]
  },
  {
    name: "Cloudinary",
    freeStorage: "25GB",
    maxFileSize: "100MB per file",
    creditCardRequired: false,
    pricing: "$99/month for paid plans",
    bestFor: ["Media files", "Image/video processing", "CDN delivery"],
    setupInstructions: `
1. Sign up at cloudinary.com
2. Get your cloud name, API key, and API secret
3. Add to .env:
   CLOUDINARY_CLOUD_NAME=your_cloud_name
   CLOUDINARY_API_KEY=your_api_key
   CLOUDINARY_API_SECRET=your_api_secret
    `,
    pros: [
      "25GB free storage",
      "Excellent media processing",
      "Global CDN included",
      "Direct browser uploads",
      "Auto optimization"
    ],
    cons: [
      "100MB file size limit",
      "Expensive paid plans",
      "Primarily for media files"
    ]
  },
  {
    name: "Supabase Storage",
    freeStorage: "1GB",
    maxFileSize: "Unlimited",
    creditCardRequired: false,
    pricing: "$25/month for 100GB",
    bestFor: ["Full-stack apps", "PostgreSQL integration", "Real-time features"],
    setupInstructions: `
1. Create project at supabase.com
2. Go to Settings > API
3. Add to .env:
   SUPABASE_URL=your_project_url
   SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_KEY=your_service_key
    `,
    pros: [
      "No file size limits",
      "PostgreSQL database included",
      "Real-time subscriptions",
      "Built-in authentication",
      "Resumable uploads"
    ],
    cons: [
      "Only 1GB free storage",
      "More expensive for storage only",
      "Requires database setup"
    ]
  },
  {
    name: "Firebase Storage",
    freeStorage: "5GB",
    maxFileSize: "32TB per file",
    creditCardRequired: false,
    pricing: "Pay-as-you-go after free tier",
    bestFor: ["Google ecosystem", "Real-time apps", "Mobile apps"],
    setupInstructions: `
1. Create Firebase project at console.firebase.google.com
2. Enable Storage in console
3. Get config from Project Settings
4. Add to .env:
   FIREBASE_API_KEY=your_api_key
   FIREBASE_PROJECT_ID=your_project_id
   FIREBASE_STORAGE_BUCKET=your_bucket
    `,
    pros: [
      "5GB free storage",
      "Massive file size limit",
      "Google ecosystem integration",
      "Automatic retry/resume",
      "Real-time database options"
    ],
    cons: [
      "1GB daily download limit",
      "Requires Firebase ecosystem",
      "Can be complex for simple storage"
    ]
  },
  {
    name: "Uploadcare",
    freeStorage: "3GB",
    maxFileSize: "100MB per file",
    creditCardRequired: false,
    pricing: "$25/month for paid plans",
    bestFor: ["Media processing", "CDN delivery", "File transformations"],
    setupInstructions: `
1. Sign up at uploadcare.com
2. Get your public and secret keys
3. Add to .env:
   UPLOADCARE_PUBLIC_KEY=your_public_key
   UPLOADCARE_SECRET_KEY=your_secret_key
    `,
    pros: [
      "3GB free storage",
      "Advanced media processing",
      "Global CDN",
      "Direct uploads",
      "No credit card required"
    ],
    cons: [
      "100MB file size limit",
      "Limited free storage",
      "Expensive for large storage needs"
    ]
  },
  {
    name: "ImageKit",
    freeStorage: "20GB",
    maxFileSize: "25MB per file",
    creditCardRequired: false,
    pricing: "$20/month for paid plans",
    bestFor: ["Image optimization", "Media delivery", "Web performance"],
    setupInstructions: `
1. Sign up at imagekit.io
2. Get your URL endpoint and keys
3. Add to .env:
   IMAGEKIT_URL_ENDPOINT=your_url_endpoint
   IMAGEKIT_PUBLIC_KEY=your_public_key
   IMAGEKIT_PRIVATE_KEY=your_private_key
    `,
    pros: [
      "20GB free storage",
      "Excellent image optimization",
      "Global CDN",
      "Real-time transformations",
      "Good free tier"
    ],
    cons: [
      "25MB file size limit",
      "Primarily for images/media",
      "Limited to media files"
    ]
  }
]

/**
 * Get recommended storage service based on requirements
 */
export function getRecommendedService(requirements: {
  maxFileSize: number // in bytes
  storageNeeded: number // in GB
  mediaProcessing?: boolean
  costSensitive?: boolean
}) {
  const { maxFileSize, storageNeeded, mediaProcessing, costSensitive } = requirements
  
  // File size over 100MB - use services without limits (but note: we're optimizing for free storage)
  if (maxFileSize > 100 * 1024 * 1024) {
    if (costSensitive) {
      return STORAGE_SERVICES.find(s => s.name === "Supabase Storage")
    } else {
      return [
        STORAGE_SERVICES.find(s => s.name === "Supabase Storage"),
        STORAGE_SERVICES.find(s => s.name === "Firebase Storage")
      ].filter(Boolean)
    }
  }
  
  // Media processing needed
  if (mediaProcessing) {
    return [
      STORAGE_SERVICES.find(s => s.name === "Cloudinary"),
      STORAGE_SERVICES.find(s => s.name === "ImageKit"),
      STORAGE_SERVICES.find(s => s.name === "Uploadcare")
    ].filter(Boolean)
  }
  
  // High storage needs
  if (storageNeeded > 10) {
    return [
      STORAGE_SERVICES.find(s => s.name === "Cloudinary"),
      STORAGE_SERVICES.find(s => s.name === "ImageKit")
    ].filter(Boolean)
  }
  
  // Default recommendation - Netlify Blobs for best free storage
  return STORAGE_SERVICES.find(s => s.name === "Netlify Blobs")
}
