import { prisma } from '../prisma'
import { DatabaseContext } from './gemini'

/**
 * AI Training service to provide context about the platform
 */
export class AITrainingService {
  /**
   * Get comprehensive database context for AI
   */
  async getDatabaseContext(): Promise<DatabaseContext> {
    try {
      const [userStats, recentActivity] = await Promise.all([
        this.getUserStats(),
        this.getRecentActivity()
      ])

      return {
        tables: this.getDatabaseTables(),
        userStats,
        recentActivity
      }
    } catch (error) {
      console.error('Error getting database context:', error)
      return {
        tables: [],
        userStats: { totalUsers: 0, totalApps: 0, pendingApps: 0, approvedApps: 0 },
        recentActivity: []
      }
    }
  }

  /**
   * Get database table information
   */
  private getDatabaseTables(): string[] {
    return [
      'User - stores user accounts and preferences',
      'App - stores application information and metadata',
      'AppVersion - tracks different versions of apps',
      'AppComment - user reviews and ratings',
      'EmailLog - email notification history',
      'EmailTemplate - email templates for notifications',
      'Account - OAuth account connections',
      'Session - user session management'
    ]
  }

  /**
   * Get user and app statistics
   */
  private async getUserStats() {
    const [totalUsers, totalApps, pendingApps, approvedApps] = await Promise.all([
      prisma.user.count(),
      prisma.app.count(),
      prisma.app.count({ where: { status: 'PENDING' } }),
      prisma.app.count({ where: { status: 'APPROVED' } })
    ])

    return {
      totalUsers,
      totalApps,
      pendingApps,
      approvedApps
    }
  }

  /**
   * Get recent platform activity
   */
  private async getRecentActivity() {
    try {
      const [recentApps, recentUsers, recentComments] = await Promise.all([
        prisma.app.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' },
          select: {
            name: true,
            status: true,
            createdAt: true,
            category: true
          }
        }),
        prisma.user.findMany({
          take: 3,
          orderBy: { createdAt: 'desc' },
          select: {
            name: true,
            createdAt: true,
            isDeveloper: true
          }
        }),
        prisma.appComment.findMany({
          take: 3,
          orderBy: { createdAt: 'desc' },
          include: {
            app: { select: { name: true } },
            user: { select: { name: true } }
          }
        })
      ])

      const activity = []

      // Add recent apps
      for (const app of recentApps) {
        activity.push({
          type: 'App Created',
          description: `${app.name} (${app.category}) - Status: ${app.status}`,
          timestamp: app.createdAt
        })
      }

      // Add recent users
      for (const user of recentUsers) {
        activity.push({
          type: 'User Registered',
          description: `${user.name || 'Anonymous'} joined as ${user.isDeveloper ? 'Developer' : 'User'}`,
          timestamp: user.createdAt
        })
      }

      // Add recent comments
      for (const comment of recentComments) {
        activity.push({
          type: 'App Review',
          description: `${comment.user.name || 'Anonymous'} reviewed ${comment.app.name}`,
          timestamp: comment.createdAt
        })
      }

      // Sort by timestamp and return latest 10
      return activity
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 10)

    } catch (error) {
      console.error('Error getting recent activity:', error)
      return []
    }
  }

  /**
   * Get app categories and their popularity
   */
  async getAppCategoryStats() {
    try {
      const categories = await prisma.app.groupBy({
        by: ['category'],
        _count: {
          category: true
        },
        orderBy: {
          _count: {
            category: 'desc'
          }
        }
      })

      return categories.map((cat: { category: string; _count: { category: number } }) => ({
        category: cat.category,
        count: cat._count.category
      }))
    } catch (error) {
      console.error('Error getting category stats:', error)
      return []
    }
  }

  /**
   * Get user engagement metrics
   */
  async getUserEngagementMetrics() {
    try {
      const [
        developersCount,
        activeAppsCount,
        totalDownloads,
        averageRating
      ] = await Promise.all([
        prisma.user.count({ where: { isDeveloper: true } }),
        prisma.app.count({ where: { status: 'APPROVED' } }),
        prisma.app.aggregate({ _sum: { downloads: true } }),
        prisma.appComment.aggregate({ _avg: { rating: true } })
      ])

      return {
        developersCount,
        activeAppsCount,
        totalDownloads: totalDownloads._sum.downloads || 0,
        averageRating: averageRating._avg.rating || 0
      }
    } catch (error) {
      console.error('Error getting engagement metrics:', error)
      return {
        developersCount: 0,
        activeAppsCount: 0,
        totalDownloads: 0,
        averageRating: 0
      }
    }
  }

  /**
   * Get app approval insights
   */
  async getAppApprovalInsights() {
    try {
      const [statusCounts, avgApprovalTime] = await Promise.all([
        prisma.app.groupBy({
          by: ['status'],
          _count: { status: true }
        }),
        this.getAverageApprovalTime()
      ])

      const statusMap = statusCounts.reduce((acc: Record<string, number>, item: { status: string; _count: { status: number } }) => {
        acc[item.status] = item._count.status
        return acc
      }, {} as Record<string, number>)

      return {
        pending: statusMap.PENDING || 0,
        approved: statusMap.APPROVED || 0,
        rejected: statusMap.REJECTED || 0,
        suspended: statusMap.SUSPENDED || 0,
        avgApprovalTime
      }
    } catch (error) {
      console.error('Error getting approval insights:', error)
      return {
        pending: 0,
        approved: 0,
        rejected: 0,
        suspended: 0,
        avgApprovalTime: 0
      }
    }
  }

  /**
   * Calculate average approval time
   */
  private async getAverageApprovalTime(): Promise<number> {
    try {
      const approvedApps = await prisma.app.findMany({
        where: { status: 'APPROVED' },
        select: {
          createdAt: true,
          updatedAt: true
        }
      })

      if (approvedApps.length === 0) return 0

      const totalTime = approvedApps.reduce((sum: number, app: { createdAt: Date; updatedAt: Date }) => {
        return sum + (app.updatedAt.getTime() - app.createdAt.getTime())
      }, 0)

      // Return average time in hours
      return Math.round(totalTime / approvedApps.length / (1000 * 60 * 60))
    } catch (error) {
      console.error('Error calculating approval time:', error)
      return 0
    }
  }

  /**
   * Get trending tags
   */
  async getTrendingTags(limit = 10) {
    try {
      const apps = await prisma.app.findMany({
        select: { tags: true },
        where: { status: 'APPROVED' }
      })

      const tagCounts = new Map<string, number>()

      apps.forEach(app => {
        app.tags.forEach(tag => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
        })
      })

      return Array.from(tagCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([tag, count]) => ({ tag, count }))
    } catch (error) {
      console.error('Error getting trending tags:', error)
      return []
    }
  }

  /**
   * Get platform health score
   */
  async getPlatformHealthScore(): Promise<{
    score: number
    factors: Array<{ name: string; score: number; weight: number }>
  }> {
    try {
      const [userStats, approvalInsights, engagement] = await Promise.all([
        this.getUserStats(),
        this.getAppApprovalInsights(),
        this.getUserEngagementMetrics()
      ])

      const factors = [
        {
          name: 'User Growth',
          score: Math.min(userStats.totalUsers / 100, 1) * 100, // Scale to 100 users = 100%
          weight: 0.2
        },
        {
          name: 'App Quality',
          score: userStats.totalApps > 0 ? (approvalInsights.approved / userStats.totalApps) * 100 : 0,
          weight: 0.3
        },
        {
          name: 'User Engagement',
          score: Math.min(engagement.totalDownloads / 1000, 1) * 100, // Scale to 1000 downloads = 100%
          weight: 0.25
        },
        {
          name: 'Content Moderation',
          score: userStats.totalApps > 0 ? Math.max(0, 100 - (approvalInsights.pending / userStats.totalApps) * 200) : 100,
          weight: 0.25
        }
      ]

      const score = factors.reduce((sum: number, factor: { score: number; weight: number }) => sum + (factor.score * factor.weight), 0)

      return { score: Math.round(score), factors }
    } catch (error) {
      console.error('Error calculating platform health:', error)
      return {
        score: 0,
        factors: []
      }
    }
  }
}

// Export singleton instance
export const aiTrainingService = new AITrainingService()
