/**
 * Netlify Blobs Storage Service
 * Provides 2GB free storage forever
 */

import { getStore } from '@netlify/blobs'

export interface NetlifyStorageConfig {
  siteId: string
  token: string
}

export interface UploadResult {
  success: boolean
  url?: string
  key?: string
  error?: string
}

/**
 * Netlify Blobs Storage Service
 * Free 2GB storage with proper SDK implementation
 */
export class NetlifyStorageService {
  private config: NetlifyStorageConfig

  constructor() {
    this.config = {
      siteId: process.env.NETLIFY_SITE_ID || '',
      token: process.env.NETLIFY_TOKEN || ''
    }
  }

  /**
   * Check if Netlify storage is properly configured
   */
  isConfigured(): boolean {
    return !!(this.config.siteId && this.config.token)
  }

  /**
   * Get Netlify Blobs store instance
   */
  private getStore(storeName: string = 'devportal-files') {
    return getStore({
      name: storeName,
      siteID: this.config.siteId,
      token: this.config.token
    })
  }

  /**
   * Upload a file to Netlify Blobs
   * Uses base64 encoding for reliable storage
   */
  async uploadFile(
    buffer: Buffer,
    key: string,
    contentType: string,
    metadata?: Record<string, string>
  ): Promise<UploadResult> {
    try {
      console.log(`[NETLIFY BLOBS] Uploading file: ${key} (${buffer.length} bytes)`)

      // Check file size limit (100MB for free tier)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (buffer.length > maxSize) {
        throw new Error(`File too large: ${buffer.length} bytes. Maximum allowed: ${maxSize} bytes (100MB)`)
      }

      const store = this.getStore()
      
      // Convert to base64 string for reliable storage
      // This avoids TypeScript issues with Buffer/ArrayBuffer types
      const base64Data = buffer.toString('base64')
      
      // Upload the file as base64 string
      await store.set(key, base64Data, {
        metadata: {
          contentType,
          uploadDate: new Date().toISOString(),
          size: buffer.length.toString(),
          encoding: 'base64',
          ...metadata
        }
      })

      // Generate public URL using our internal file serving API
      const publicUrl = `/api/files/${key}`

      console.log(`[NETLIFY BLOBS] Upload successful: ${publicUrl}`)

      return {
        success: true,
        url: publicUrl,
        key
      }
    } catch (error) {
      console.error('[NETLIFY BLOBS] Upload failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Delete a file from Netlify Blobs
     /**
   * Delete a file from Netlify Blobs
   */
  async deleteFile(key: string): Promise<boolean> {
    try {
      console.log(`[NETLIFY BLOBS] Deleting file: ${key}`)

      const store = this.getStore()
      await store.delete(key)

      console.log(`[NETLIFY BLOBS] File deleted: ${key}`)
      return true
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to delete file:', error)
      return false
    }
  }

  /**
   * Download a file from Netlify Blobs
   */
  async downloadFile(key: string): Promise<Buffer | null> {
    try {
      console.log(`[NETLIFY BLOBS] Downloading file: ${key}`)

      const store = this.getStore()
      const result = await store.get(key, { type: 'text' })

      if (!result) {
        return null
      }

      // Get metadata to check encoding
      const metadataResult = await store.getMetadata(key)
      const encoding = metadataResult?.metadata?.encoding
      
      let buffer: Buffer
      if (encoding === 'base64') {
        // Decode from base64
        buffer = Buffer.from(result, 'base64')
      } else {
        // Fallback for non-base64 data
        buffer = Buffer.from(result)
      }

      console.log(`[NETLIFY BLOBS] File downloaded: ${key} (${buffer.length} bytes)`)
      return buffer
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to download file:', error)
      return null
    }
  }

  /**
   * List files in Netlify Blobs
   */
  async listFiles(prefix?: string): Promise<string[]> {
    try {
      const store = this.getStore()
      const result = await store.list(prefix ? { prefix } : undefined)

      // Extract keys from the result
      const keys = result.blobs?.map(blob => blob.key) || []
      return keys
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to list files:', error)
      return []
    }
  }

  /**
   * Get file metadata from Netlify Blobs
   */
  async getFileMetadata(key: string): Promise<Record<string, any> | null> {
    try {
      const store = this.getStore()
      const result = await store.getMetadata(key)

      if (!result) {
        return null
      }

      return result
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to get metadata:', error)
      return null
    }
  }

  /**
   * Check if a file exists in Netlify Blobs
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const store = this.getStore()
      const result = await store.get(key, { type: 'text' })
      return !!result
    } catch (error) {
      return false
    }
  }

  /**
   * Generate a storage key for an app file
   */
  generateFileKey(appId: string, fileName: string, fileType: string): string {
    const timestamp = Date.now()
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
    const fileTypePrefix = fileType ? `${fileType}/` : ''
    return `apps/${appId}/${fileTypePrefix}${timestamp}_${sanitizedFileName}`
  }

  /**
   * Get storage usage statistics
   * Note: Netlify doesn't provide direct usage API, so this returns estimated values
   */
  async getStorageStats(): Promise<{ used: number; total: number } | null> {
    try {
      // Netlify Blobs doesn't provide direct usage stats via SDK
      // We'll return the total limit for now
      return {
        used: 0, // Cannot determine actual usage without listing all files
        total: 2 * 1024 * 1024 * 1024 // 2GB in bytes
      }
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to get storage stats:', error)
      return null
    }
  }

  /**
   * Extract storage key from URL
   */
  getKeyFromUrl(url: string): string | null {
    try {
      // Extract key from Netlify Blobs URL pattern
      // URLs look like: https://site-id.netlify.app/.netlify/blobs/store-name/key
      const blobsPattern = new RegExp(`https://${this.config.siteId}.netlify.app/.netlify/blobs/devportal-files/(.+)`)
      const match = url.match(blobsPattern)
      
      if (match && match[1]) {
        return match[1]
      }
      
      // Fallback: try to extract from path
      const urlObj = new URL(url)
      const pathParts = urlObj.pathname.split('/')
      const blobsIndex = pathParts.indexOf('blobs')
      
      if (blobsIndex !== -1 && pathParts.length > blobsIndex + 2) {
        // Return everything after the store name
        return pathParts.slice(blobsIndex + 2).join('/')
      }
      
      return null
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to extract key from URL:', error)
      return null
    }
  }

  /**
   * Get file size without downloading the entire file
   */
  async getFileSize(key: string): Promise<number | null> {
    try {
      const metadata = await this.getFileMetadata(key)
      if (metadata && metadata.size) {
        return parseInt(metadata.size, 10)
      }
      return null
    } catch (error) {
      console.error('[NETLIFY BLOBS] Failed to get file size:', error)
      return null
    }
  }
}

// Export singleton instance
export const netlifyStorageService = new NetlifyStorageService()
