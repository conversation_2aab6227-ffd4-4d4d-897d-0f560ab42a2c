/**
 * Simplified Storage Service using Netlify Blobs
 * Optimized for 2GB free storage with no virus scanning
 */

import { netlifyStorageService } from './netlifyStorage'

export interface StorageUploadResult {
  success: boolean
  url?: string
  key?: string
  error?: string
}

/**
 * Main storage service - simplified and optimized
 */
export class StorageService {
  /**
   * Check if storage is configured
   */
  isConfigured(): boolean {
    return netlifyStorageService.isConfigured()
  }

  /**
   * Upload a file to storage
   */
  async uploadFile(
    buffer: Buffer,
    fileName: string,
    contentType: string,
    appId: string,
    fileType: string = 'unknown'
  ): Promise<StorageUploadResult> {
    try {
      const key = this.generateFileKey(appId, fileName, fileType)
      
      return await netlifyStorageService.uploadFile(
        buffer,
        key,
        contentType,
        {
          originalFileName: fileName,
          appId,
          fileType,
          uploadDate: new Date().toISOString()
        }
      )
    } catch (error) {
      console.error('[STORAGE] Upload failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(key: string): Promise<boolean> {
    return await netlifyStorageService.deleteFile(key)
  }

  /**
   * Download a file from storage
   */
  async downloadFile(key: string): Promise<Buffer | null> {
    return await netlifyStorageService.downloadFile(key)
  }

  /**
   * Generate a unique storage key
   */
  generateFileKey(appId: string, fileName: string, fileType: string): string {
    return netlifyStorageService.generateFileKey(appId, fileName, fileType)
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats(): Promise<{ used: number; total: number; percentage: number } | null> {
    const stats = await netlifyStorageService.getStorageStats()
    if (!stats) return null

    return {
      ...stats,
      percentage: Math.round((stats.used / stats.total) * 100)
    }
  }

  /**
   * List files for an app
   */
  async listAppFiles(appId: string): Promise<string[]> {
    return await netlifyStorageService.listFiles(`apps/${appId}/`)
  }

  /**
   * Clean up files for an app
   */
  async cleanupAppFiles(appId: string): Promise<number> {
    try {
      const files = await this.listAppFiles(appId)
      let deletedCount = 0

      for (const file of files) {
        const deleted = await this.deleteFile(file)
        if (deleted) {
          deletedCount++
          console.log(`[STORAGE CLEANUP] Deleted file: ${file}`)
        }
      }

      return deletedCount
    } catch (error) {
      console.error('[STORAGE CLEANUP] Failed to cleanup app files:', error)
      return 0
    }
  }

  /**
   * Extract storage key from URL
   */
  getKeyFromUrl(url: string): string | null {
    return netlifyStorageService.getKeyFromUrl(url)
  }
}

// Export singleton instance
export const storageService = new StorageService()
