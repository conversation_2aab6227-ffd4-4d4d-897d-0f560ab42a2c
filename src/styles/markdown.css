/* Custom markdown styles for AI chat interface */

.markdown-content {
  line-height: 1.6;
}

/* Override highlight.js styles for better dark theme integration */
.markdown-content pre code.hljs {
  background: transparent !important;
  color: #e4e4e7 !important;
  padding: 0 !important;
}

/* Syntax highlighting color overrides for dark theme */
.markdown-content .hljs-keyword {
  color: #8b5cf6 !important; /* Purple for keywords */
}

.markdown-content .hljs-string {
  color: #10b981 !important; /* Green for strings */
}

.markdown-content .hljs-number {
  color: #f59e0b !important; /* Amber for numbers */
}

.markdown-content .hljs-comment {
  color: #6b7280 !important; /* Gray for comments */
}

.markdown-content .hljs-function {
  color: #3b82f6 !important; /* Blue for functions */
}

.markdown-content .hljs-variable {
  color: #ef4444 !important; /* Red for variables */
}

.markdown-content .hljs-title {
  color: #06b6d4 !important; /* <PERSON>an for titles */
}

.markdown-content .hljs-attr {
  color: #f97316 !important; /* Orange for attributes */
}

.markdown-content .hljs-built_in {
  color: #8b5cf6 !important; /* Purple for built-ins */
}

.markdown-content .hljs-literal {
  color: #ec4899 !important; /* Pink for literals */
}

/* Custom scrollbar for code blocks */
.markdown-content pre::-webkit-scrollbar {
  height: 8px;
}

.markdown-content pre::-webkit-scrollbar-track {
  background: #27272a;
  border-radius: 4px;
}

.markdown-content pre::-webkit-scrollbar-thumb {
  background: #52525b;
  border-radius: 4px;
}

.markdown-content pre::-webkit-scrollbar-thumb:hover {
  background: #71717a;
}

/* Improve spacing for nested elements */
.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul:last-child,
.markdown-content ol:last-child {
  margin-bottom: 0;
}

/* Better table styling */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #3f3f46;
}

.markdown-content th {
  background-color: #27272a;
}

.markdown-content tr:nth-child(even) {
  background-color: #18181b;
}

/* Blockquote styling */
.markdown-content blockquote {
  margin: 0;
}

.markdown-content blockquote p {
  margin-bottom: 0;
}

/* Code block copy button styling */
.code-block-container {
  position: relative;
}

.code-block-copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #3f3f46;
  border: 1px solid #52525b;
  color: #d4d4d8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.code-block-container:hover .code-block-copy-button {
  opacity: 1;
}

.code-block-copy-button:hover {
  background: #52525b;
  color: #ffffff;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .markdown-content pre {
    padding: 12px;
    font-size: 13px;
  }
  
  .markdown-content code {
    font-size: 13px;
  }
  
  .markdown-content table {
    font-size: 14px;
  }
}

/* Animation for new content */
.markdown-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
