'use client'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { ChatInterface } from '@/components/ai/ChatInterface'
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import {
  Bot,
  <PERSON>rkles,
  TrendingUp,
  Users,
  Package,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react'

interface PlatformStats {
  totalApps: number
  approvedApps: number
  pendingApps: number
  totalUsers: number
}

interface AIStatus {
  configured: boolean
  available: boolean
  model: string
}

export default function AvenaAIPage() {
  const t = useTranslations()
  const [stats, setStats] = useState<PlatformStats | null>(null)
  const [aiStatus, setAIStatus] = useState<AIStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAIStatus()
  }, [])

  const fetchAIStatus = async () => {
    try {
      const response = await fetch('/api/ai/chat')
      if (response.ok) {
        const data = await response.json()
        setAIStatus(data.status)
        setStats(data.context.platformStats)
      }
    } catch (error) {
      console.error('Failed to fetch AI status:', error)
    } finally {
      setLoading(false)
    }
  }

  const quickActions = [
    {
      title: 'App Development Help',
      description: 'Get guidance on app development best practices',
      prompt: 'How can I improve my app for better approval chances?',
      icon: Package,
      color: 'blue'
    },
    {
      title: 'Platform Statistics',
      description: 'View current platform metrics and trends',
      prompt: 'Show me the current platform statistics and trends',
      icon: TrendingUp,
      color: 'green'
    },
    {
      title: 'Approval Process',
      description: 'Learn about the app approval workflow',
      prompt: 'Explain the app approval process and timeline',
      icon: CheckCircle,
      color: 'purple'
    },
    {
      title: 'Troubleshooting',
      description: 'Get help with common issues and problems',
      prompt: 'Help me troubleshoot issues with my app submission',
      icon: AlertCircle,
      color: 'orange'
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
              <Bot className="w-7 h-7 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
                Avena AI
              </h1>
              <p className="text-zinc-400 mt-1">
                Your intelligent assistant for the AveHub Developer Portal
              </p>
            </div>
          </div>

          {/* AI Status */}
          <div className="flex items-center space-x-2 px-4 py-2 bg-zinc-900/50 rounded-xl border border-zinc-800/50">
            <div className={`w-2 h-2 rounded-full ${aiStatus?.available ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
            <span className="text-sm text-zinc-300">
              {loading ? 'Checking...' : aiStatus?.available ? 'Online' : 'Offline'}
            </span>
          </div>
        </div>

        {/* Platform Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
                  <Package className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-white">{stats.totalApps}</p>
                  <p className="text-sm text-zinc-400">Total Apps</p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-white">{stats.approvedApps}</p>
                  <p className="text-sm text-zinc-400">Approved</p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-600/20 rounded-lg flex items-center justify-center">
                  <Clock className="w-5 h-5 text-orange-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-white">{stats.pendingApps}</p>
                  <p className="text-sm text-zinc-400">Pending</p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-white">{stats.totalUsers}</p>
                  <p className="text-sm text-zinc-400">Users</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <div className="lg:col-span-1 space-y-4">
            <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Sparkles className="w-5 h-5 text-yellow-400" />
                <h3 className="text-lg font-semibold text-white">Quick Actions</h3>
              </div>

              <div className="space-y-3">
                {quickActions.map((action, index) => (
                  <button
                    key={index}
                    className="w-full text-left p-3 bg-zinc-800/30 hover:bg-zinc-800/50 rounded-lg border border-zinc-700/50 hover:border-zinc-600/50 transition-all duration-200 group"
                    onClick={() => {
                      // This will be handled by the ChatInterface component
                      const event = new CustomEvent('avena-quick-action', {
                        detail: { prompt: action.prompt }
                      })
                      window.dispatchEvent(event)
                    }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`w-8 h-8 bg-${action.color}-600/20 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-${action.color}-600/30 transition-colors`}>
                        <action.icon className={`w-4 h-4 text-${action.color}-400`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-white group-hover:text-zinc-100">
                          {action.title}
                        </p>
                        <p className="text-xs text-zinc-400 mt-1">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* AI Model Info */}
            {aiStatus && (
              <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">AI Information</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-zinc-400">Model:</span>
                    <span className="text-white">{aiStatus.model}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-zinc-400">Status:</span>
                    <span className={`${aiStatus.available ? 'text-green-400' : 'text-red-400'}`}>
                      {aiStatus.available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-zinc-400">Provider:</span>
                    <span className="text-white">Google Gemini</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-zinc-400">Features:</span>
                    <span className="text-white">Markdown Support</span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-zinc-800/30 rounded-lg border border-zinc-700/50">
                  <p className="text-xs text-zinc-400 mb-2">Enhanced Features:</p>
                  <ul className="text-xs text-zinc-300 space-y-1">
                    <li>• Code syntax highlighting</li>
                    <li>• Markdown formatting</li>
                    <li>• Copy code blocks</li>
                    <li>• Interactive suggestions</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <ChatInterface />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
