'use client'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { LanguageSelector } from '@/components/ui/LanguageSelector'
import { useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  Palette,
  Bell,
  Shield,
  Globe,
  Download,
  Upload,
  Moon,
  Sun,
  Monitor,
  Check
} from 'lucide-react'

interface SettingsState {
  theme: 'light' | 'dark' | 'system'
  notifications: {
    email: boolean
    push: boolean
    appUpdates: boolean
    security: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private'
    showEmail: boolean
    analytics: boolean
  }
  language: string
}

export default function SettingsPage() {
  const t = useTranslations()
  const [settings, setSettings] = useState<SettingsState>({
    theme: 'dark',
    notifications: {
      email: true,
      push: true,
      appUpdates: true,
      security: true
    },
    privacy: {
      profileVisibility: 'public',
      showEmail: false,
      analytics: true
    },
    language: 'en'
  })

  const [isSaving, setIsSaving] = useState(false)

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true)
      // TODO: Implement settings save API
      console.log('Saving settings:', settings)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleExportData = () => {
    // TODO: Implement data export
    console.log('Exporting user data...')
  }

  const handleImportData = () => {
    // TODO: Implement data import
    console.log('Importing user data...')
  }

  const updateSetting = (path: string, value: any) => {
    setSettings(prev => {
      const keys = path.split('.')
      const newSettings = { ...prev }
      let current: any = newSettings
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] }
        current = current[keys[i]]
      }
      
      current[keys[keys.length - 1]] = value
      return newSettings
    })
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
              {t('settings.title')}
            </h1>
            <p className="text-zinc-400 mt-2">
              {t('settings.subtitle')}
            </p>
          </div>
          <button
            onClick={handleSaveSettings}
            disabled={isSaving}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-colors"
          >
            <Check className="w-4 h-4" />
            <span>{isSaving ? t('common.saving') : t('settings.saveChanges')}</span>
          </button>
        </div>

        {/* Theme Settings */}
        <div className="glass rounded-xl p-6 border border-zinc-800/50">
          <div className="flex items-center space-x-3 mb-6">
            <Palette className="w-5 h-5 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">{t('settings.appearance.title')}</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-3">{t('settings.appearance.theme')}</label>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { value: 'light', label: t('settings.appearance.light'), icon: Sun },
                  { value: 'dark', label: t('settings.appearance.dark'), icon: Moon },
                  { value: 'system', label: t('settings.appearance.system'), icon: Monitor }
                ].map(({ value, label, icon: Icon }) => (
                  <button
                    key={value}
                    onClick={() => updateSetting('theme', value)}
                    className={`flex items-center space-x-3 p-4 rounded-xl border transition-all duration-200 ${
                      settings.theme === value
                        ? 'bg-blue-600/20 border-blue-500/50 text-blue-400'
                        : 'bg-zinc-900/30 border-zinc-800/50 text-zinc-400 hover:bg-zinc-900/50 hover:border-zinc-700/50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="glass rounded-xl p-6 border border-zinc-800/50">
          <div className="flex items-center space-x-3 mb-6">
            <Bell className="w-5 h-5 text-emerald-400" />
            <h2 className="text-xl font-semibold text-white">{t('settings.notifications.title')}</h2>
          </div>
          
          <div className="space-y-4">
            {[
              { key: 'email', label: t('settings.notifications.email'), description: t('settings.notifications.emailDescription') },
              { key: 'push', label: t('settings.notifications.push'), description: t('settings.notifications.pushDescription') },
              { key: 'appUpdates', label: t('settings.notifications.appUpdates'), description: t('settings.notifications.appUpdatesDescription') },
              { key: 'security', label: t('settings.notifications.security'), description: t('settings.notifications.securityDescription') }
            ].map(({ key, label, description }) => (
              <div key={key} className="flex items-center justify-between p-4 bg-zinc-900/30 rounded-xl">
                <div>
                  <h3 className="font-medium text-white">{label}</h3>
                  <p className="text-sm text-zinc-400">{description}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications[key as keyof typeof settings.notifications]}
                    onChange={(e) => updateSetting(`notifications.${key}`, e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-zinc-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Privacy Settings */}
        <div className="glass rounded-xl p-6 border border-zinc-800/50">
          <div className="flex items-center space-x-3 mb-6">
            <Shield className="w-5 h-5 text-purple-400" />
            <h2 className="text-xl font-semibold text-white">{t('settings.privacy.title')}</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-zinc-900/30 rounded-xl">
              <div>
                <h3 className="font-medium text-white">{t('settings.privacy.profileVisibility')}</h3>
                <p className="text-sm text-zinc-400">{t('settings.privacy.profileVisibilityDescription')}</p>
              </div>
              <select
                value={settings.privacy.profileVisibility}
                onChange={(e) => updateSetting('privacy.profileVisibility', e.target.value)}
                className="px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              >
                <option value="public">{t('settings.privacy.public')}</option>
                <option value="private">{t('settings.privacy.private')}</option>
              </select>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-zinc-900/30 rounded-xl">
              <div>
                <h3 className="font-medium text-white">{t('settings.privacy.showEmail')}</h3>
                <p className="text-sm text-zinc-400">{t('settings.privacy.showEmailDescription')}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.privacy.showEmail}
                  onChange={(e) => updateSetting('privacy.showEmail', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-zinc-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-zinc-900/30 rounded-xl">
              <div>
                <h3 className="font-medium text-white">{t('settings.privacy.analytics')}</h3>
                <p className="text-sm text-zinc-400">{t('settings.privacy.analyticsDescription')}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.privacy.analytics}
                  onChange={(e) => updateSetting('privacy.analytics', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-zinc-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Language & Region */}
        <div className="glass rounded-xl p-6 border border-zinc-800/50">
          <div className="flex items-center space-x-3 mb-6">
            <Globe className="w-5 h-5 text-amber-400" />
            <h2 className="text-xl font-semibold text-white">{t('settings.language.title')}</h2>
          </div>
          
          <LanguageSelector />
        </div>

        {/* Data Management */}
        <div className="glass rounded-xl p-6 border border-zinc-800/50">
          <div className="flex items-center space-x-3 mb-6">
            <Download className="w-5 h-5 text-cyan-400" />
            <h2 className="text-xl font-semibold text-white">{t('settings.dataManagement.title')}</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={handleExportData}
              className="flex items-center space-x-3 p-4 bg-zinc-900/30 hover:bg-zinc-900/50 border border-zinc-800/50 hover:border-zinc-700/50 rounded-xl transition-all duration-200"
            >
              <Download className="w-5 h-5 text-emerald-400" />
              <div className="text-left">
                <h3 className="font-medium text-white">{t('settings.dataManagement.exportData')}</h3>
                <p className="text-sm text-zinc-400">{t('settings.dataManagement.exportDataDescription')}</p>
              </div>
            </button>
            
            <button
              onClick={handleImportData}
              className="flex items-center space-x-3 p-4 bg-zinc-900/30 hover:bg-zinc-900/50 border border-zinc-800/50 hover:border-zinc-700/50 rounded-xl transition-all duration-200"
            >
              <Upload className="w-5 h-5 text-blue-400" />
              <div className="text-left">
                <h3 className="font-medium text-white">{t('settings.dataManagement.importData')}</h3>
                <p className="text-sm text-zinc-400">{t('settings.dataManagement.importDataDescription')}</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
