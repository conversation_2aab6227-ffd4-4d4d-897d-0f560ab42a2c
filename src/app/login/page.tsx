'use client'

import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Code, Chrome } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export default function LoginPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isCheckingSession, setIsCheckingSession] = useState(true)

  useEffect(() => {
    // Check if user is already logged in
    getSession().then((session) => {
      if (session) {
        router.push('/')
      }
      setIsCheckingSession(false)
    })
  }, [router])

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    try {
      await signIn('google', { callbackUrl: '/' })
    } catch (error) {
      console.error('Sign in error:', error)
      setIsLoading(false)
    }
  }

  if (isCheckingSession) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-black py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-2xl flex items-center justify-center shadow-2xl glow-blue">
              <Code className="w-10 h-10 text-white" />
            </div>
          </div>
          <h2 className="mt-6 text-4xl font-bold bg-gradient-to-r from-white via-zinc-100 to-zinc-300 bg-clip-text text-transparent">
            Welcome to AveHub
          </h2>
          <p className="mt-3 text-lg text-zinc-400">
            Developer Portal - Sign in to your account
          </p>
        </div>

        {/* Sign In Form */}
        <div className="mt-8 space-y-6">
          <div className="glass rounded-2xl p-8 border border-zinc-800/50">
            <button
              onClick={handleGoogleSignIn}
              disabled={isLoading}
              className="group relative w-full flex justify-center py-4 px-6 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl glow-blue"
            >
              {isLoading ? (
                <LoadingSpinner size="sm" className="mr-3" />
              ) : (
                <Chrome className="w-6 h-6 mr-3" />
              )}
              {isLoading ? 'Signing in...' : 'Continue with Google'}
            </button>
          </div>

          {/* Features */}
          <div className="glass rounded-2xl p-6 border border-zinc-800/50">
            <h3 className="text-xl font-semibold text-white mb-6 text-center">
              Developer Portal Features
            </h3>
            <ul className="space-y-4 text-zinc-300">
              <li className="flex items-center">
                <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mr-4"></div>
                <span className="font-medium">Manage your applications</span>
              </li>
              <li className="flex items-center">
                <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full mr-4"></div>
                <span className="font-medium">Access API documentation</span>
              </li>
              <li className="flex items-center">
                <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full mr-4"></div>
                <span className="font-medium">Monitor app analytics</span>
              </li>
              <li className="flex items-center">
                <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mr-4"></div>
                <span className="font-medium">Collaborate with team members</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-sm text-zinc-500">
            By signing in, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </div>
  )
}
