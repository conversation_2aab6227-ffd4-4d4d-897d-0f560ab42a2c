'use client'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { useUserProfile } from '@/hooks/useUserProfile'
import { useState, useEffect } from 'react'
import {
  User,
  Mail,
  Calendar,
  Shield,
  Edit3,
  Save,
  X,
  Trash2,
  <PERSON><PERSON><PERSON>riangle,
  RefreshCw
} from 'lucide-react'
import { Avatar } from '@/components/ui/Avatar'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { signOut } from 'next-auth/react'

export default function ProfilePage() {
  const { user, refreshUser, isRefreshing, updateProfile, deleteAccount } = useUserProfile()
  const [isEditing, setIsEditing] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [editForm, setEditForm] = useState({
    name: user?.name || '',
    email: user?.email || ''
  })
  const [updateSuccess, setUpdateSuccess] = useState(false)
  const [updateError, setUpdateError] = useState<string | null>(null)

  // Update form when user data changes
  useEffect(() => {
    if (user) {
      setEditForm({
        name: user.name || '',
        email: user.email || ''
      })
    }
  }, [user])

  const handleSaveProfile = async () => {
    try {
      // Clear previous messages
      setUpdateError(null)
      setUpdateSuccess(false)

      // Validate form data
      if (!editForm.name.trim() || !editForm.email.trim()) {
        setUpdateError('Name and email are required')
        return
      }

      const result = await updateProfile({
        name: editForm.name.trim(),
        email: editForm.email.trim()
      })

      if (result.success) {
        setIsEditing(false)
        setUpdateSuccess(true)

        // Clear success message after 3 seconds
        setTimeout(() => {
          setUpdateSuccess(false)
        }, 3000)
      } else {
        setUpdateError(result.error || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Failed to save profile:', error)
      setUpdateError('An unexpected error occurred')
    }
  }

  const handleDeleteAccount = async () => {
    try {
      const result = await deleteAccount()

      if (result.success) {
        await signOut({ callbackUrl: '/login' })
      } else {
        alert('Failed to delete account: ' + (result.error || 'Unknown error'))
      }
    } catch (error) {
      console.error('Failed to delete account:', error)
      alert('Failed to delete account')
    }
  }

  const handleCancelEdit = () => {
    setEditForm({
      name: user?.name || '',
      email: user?.email || ''
    })
    setIsEditing(false)
    setUpdateError(null)
    setUpdateSuccess(false)
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
            Profile Settings
          </h1>
          <p className="text-zinc-400 mt-2">
            Manage your account information and preferences
          </p>
        </div>

        {/* Success/Error Messages */}
        {updateSuccess && (
          <div className="glass rounded-xl p-4 border border-emerald-500/20 bg-emerald-500/10">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-emerald-400 font-medium">Profile updated successfully!</p>
            </div>
          </div>
        )}

        {updateError && (
          <div className="glass rounded-xl p-4 border border-red-500/20 bg-red-500/10">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <X className="w-3 h-3 text-white" />
              </div>
              <p className="text-red-400 font-medium">{updateError}</p>
            </div>
          </div>
        )}

        {/* Profile Information */}
        <div className="glass rounded-xl p-6 border border-zinc-800/50">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white">Account Information</h2>
            {!isEditing && (
              <div className="flex items-center space-x-3">
                <button
                  onClick={refreshUser}
                  disabled={isRefreshing}
                  className="flex items-center space-x-2 px-3 py-2 bg-zinc-700 hover:bg-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  title="Refresh profile data"
                >
                  <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </button>
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <Edit3 className="w-4 h-4" />
                  <span>Edit Profile</span>
                </button>
              </div>
            )}
          </div>

          <div className="space-y-6">
            {/* Avatar and Basic Info */}
            <div className="flex items-center space-x-6">
              <Avatar
                src={user?.image}
                name={user?.name}
                alt={user?.name || 'User'}
                size="lg"
                className="w-20 h-20 text-xl"
              />
              <div className="flex-1">
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">Name</label>
                      <input
                        type="text"
                        value={editForm.name}
                        onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                        className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">Email</label>
                      <input
                        type="email"
                        value={editForm.email}
                        onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                        className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                      />
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={handleSaveProfile}
                        disabled={isRefreshing}
                        className="flex items-center space-x-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                      >
                        {isRefreshing ? (
                          <LoadingSpinner size="sm" />
                        ) : (
                          <Save className="w-4 h-4" />
                        )}
                        <span>{isRefreshing ? 'Saving...' : 'Save Changes'}</span>
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        disabled={isRefreshing}
                        className="flex items-center space-x-2 px-4 py-2 text-zinc-400 hover:text-white transition-colors"
                      >
                        <X className="w-4 h-4" />
                        <span>Cancel</span>
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-xl font-semibold text-white">{user?.name}</h3>
                      <p className="text-zinc-400">{user?.email}</p>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-zinc-400">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>Joined December 2024</span>
                      </div>
                      {user?.admin && (
                        <div className="flex items-center space-x-2">
                          <Shield className="w-4 h-4 text-purple-400" />
                          <span className="text-purple-400">Administrator</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Account Details */}
            {!isEditing && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-zinc-800/50">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-4 bg-zinc-900/30 rounded-xl">
                    <User className="w-5 h-5 text-blue-400" />
                    <div>
                      <p className="text-sm font-medium text-white">Account Type</p>
                      <p className="text-sm text-zinc-400">
                        {user?.isDeveloper ? 'Developer Account' : 'Standard Account'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-zinc-900/30 rounded-xl">
                    <Mail className="w-5 h-5 text-emerald-400" />
                    <div>
                      <p className="text-sm font-medium text-white">Email Status</p>
                      <p className="text-sm text-emerald-400">Verified</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-4 bg-zinc-900/30 rounded-xl">
                    <Shield className="w-5 h-5 text-amber-400" />
                    <div>
                      <p className="text-sm font-medium text-white">Security</p>
                      <p className="text-sm text-zinc-400">OAuth Authentication</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Danger Zone */}
        <div className="glass rounded-xl p-6 border border-red-500/20 bg-red-500/5">
          <h2 className="text-xl font-semibold text-red-400 mb-4">Danger Zone</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
              <div>
                <h3 className="font-medium text-white">Delete Account</h3>
                <p className="text-sm text-zinc-400 mt-1">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
              </div>
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete Account</span>
              </button>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowDeleteConfirm(false)} />

            <div className="relative w-full max-w-md bg-black/95 backdrop-blur-xl border border-red-500/30 rounded-2xl shadow-2xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Delete Account</h3>
                  <p className="text-sm text-zinc-400">This action cannot be undone</p>
                </div>
              </div>

              <p className="text-zinc-300 mb-6">
                Are you sure you want to delete your account? All your apps, data, and settings will be permanently removed.
              </p>

              <div className="flex items-center space-x-3">
                <button
                  onClick={handleDeleteAccount}
                  disabled={isRefreshing}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  {isRefreshing ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                  <span>{isRefreshing ? 'Deleting...' : 'Delete Account'}</span>
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isRefreshing}
                  className="px-4 py-3 text-zinc-400 hover:text-white transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
