import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { cleanupService } from '@/lib/cleanup'

// GET - Get cleanup statistics
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { admin: true }
    })

    if (!user?.admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const stats = await cleanupService.getCleanupStats()

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('Error fetching cleanup stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Perform cleanup operations
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { admin: true }
    })

    if (!user?.admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { operation } = body

    let result

    switch (operation) {
      case 'expired-apps':
        result = await cleanupService.cleanupExpiredPendingApps()
        break
      
      case 'orphaned-files':
        result = await cleanupService.cleanupOrphanedFiles()
        break
      
      case 'send-warnings':
        const warningsSent = await cleanupService.sendExpiryWarnings()
        result = {
          deletedApps: 0,
          deletedFiles: 0,
          errors: [],
          warnings: [],
          warningsSent
        }
        break
      
      case 'full-cleanup':
        // Perform all cleanup operations
        const expiredResult = await cleanupService.cleanupExpiredPendingApps()
        const orphanedResult = await cleanupService.cleanupOrphanedFiles()
        const warnings = await cleanupService.sendExpiryWarnings()
        
        result = {
          deletedApps: expiredResult.deletedApps,
          deletedFiles: expiredResult.deletedFiles + orphanedResult.deletedFiles,
          errors: [...expiredResult.errors, ...orphanedResult.errors],
          warnings: [...expiredResult.warnings, ...orphanedResult.warnings],
          warningsSent: warnings
        }
        break
      
      default:
        return NextResponse.json({ error: 'Invalid operation' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      operation,
      result
    })

  } catch (error) {
    console.error('Error performing cleanup:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
