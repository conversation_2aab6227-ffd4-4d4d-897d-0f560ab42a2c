import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { storageService } from '@/lib/storage'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check API key for external access
    const authHeader = request.headers.get('authorization')
    const apiKeyHeader = request.headers.get('x-api-key')
    
    const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : apiKeyHeader
    
    if (!apiKey || apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const { id: appId } = await params

    // Find the app
    const app = await prisma.app.findUnique({
      where: { id: appId },
      select: {
        id: true,
        name: true,
        downloadUrl: true,
        status: true,
        downloads: true,
        version: true,
        fileSize: true,
        developer: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!app) {
      return NextResponse.json({ 
        error: 'App not found',
        code: 'APP_NOT_FOUND'
      }, { status: 404 })
    }

    if (app.status !== 'APPROVED') {
      return NextResponse.json({ 
        error: 'App not available for download',
        code: 'APP_NOT_AVAILABLE'
      }, { status: 403 })
    }

    if (!app.downloadUrl) {
      return NextResponse.json({ 
        error: 'Download URL not available',
        code: 'DOWNLOAD_URL_MISSING'
      }, { status: 404 })
    }

    // Increment download count atomically
    await prisma.app.update({
      where: { id: appId },
      data: {
        downloads: {
          increment: 1
        }
      }
    })

    // Note: Download logging removed as downloadLog model doesn't exist in schema

    // Return download information
    const response = {
      success: true,
      app: {
        id: app.id,
        name: app.name,
        version: app.version,
        fileSize: app.fileSize,
        developer: app.developer
      },
      downloadUrl: app.downloadUrl,
      downloads: app.downloads + 1, // Return updated count
      timestamp: new Date().toISOString()
    }

    // Add CORS headers
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Cache-Control': 'no-cache' // Don't cache download responses
    }

    return NextResponse.json(response, { headers })

  } catch (error) {
    console.error('Error processing download:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}

// Handle direct file download
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check API key for external access
    const authHeader = request.headers.get('authorization')
    const apiKeyHeader = request.headers.get('x-api-key')
    
    const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : apiKeyHeader
    
    if (!apiKey || apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const { id: appId } = await params

    // Find the app
    const app = await prisma.app.findUnique({
      where: { id: appId },
      select: {
        id: true,
        name: true,
        downloadUrl: true,
        status: true,
        downloads: true,
        version: true,
        fileSize: true,
        developer: {
          select: {
            name: true
          }
        }
      }
    })

    if (!app || app.status !== 'APPROVED' || !app.downloadUrl) {
      return NextResponse.json({ error: 'App not available' }, { status: 404 })
    }

    // Increment download count
    await prisma.app.update({
      where: { id: appId },
      data: {
        downloads: { increment: 1 }
      }
    })

    // Get file from storage
    const storageKey = storageService.getKeyFromUrl(app.downloadUrl)
    if (!storageKey) {
      return NextResponse.json({ error: 'Invalid download URL' }, { status: 400 })
    }

    const fileBuffer = await storageService.downloadFile(storageKey)
    if (!fileBuffer) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Return file as download
    const fileName = `${app.name}_v${app.version}.zip`
    
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileBuffer.length.toString(),
        'Access-Control-Allow-Origin': '*'
      }
    })

  } catch (error) {
    console.error('Error downloading file:', error)
    return NextResponse.json({ error: 'Download failed' }, { status: 500 })
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  })
}
