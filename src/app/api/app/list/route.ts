import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Check API key for external access
    const authHeader = request.headers.get('authorization')
    const apiKeyHeader = request.headers.get('x-api-key')
    
    const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : apiKeyHeader
    
    if (!apiKey || apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    // Get query parameters for filtering and pagination
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured')
    const sortBy = searchParams.get('sortBy') || 'downloads' // downloads, name, createdAt, rating
    const sortOrder = searchParams.get('sortOrder') || 'desc' // asc, desc
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100) // Max 100 per page
    const skip = (page - 1) * limit

    // Build where clause for public app store
    const where: any = {
      status: 'APPROVED' // Only show approved apps
    }

    if (category) {
      where.category = category
    }

    if (featured === 'true') {
      where.featured = true
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { shortDescription: { contains: search, mode: 'insensitive' } },
        { tags: { hasSome: [search] } }
      ]
    }

    // Build orderBy clause
    let orderBy: any = {}
    switch (sortBy) {
      case 'name':
        orderBy = { name: sortOrder }
        break
      case 'createdAt':
        orderBy = { createdAt: sortOrder }
        break
      case 'rating':
        // We'll handle rating sort after fetching since it's calculated
        orderBy = { downloads: 'desc' } // Fallback to downloads
        break
      case 'downloads':
      default:
        orderBy = { downloads: sortOrder }
        break
    }

    // Fetch apps with pagination and include related data
    const [apps, total, categories] = await Promise.all([
      prisma.app.findMany({
        where,
        include: {
          developer: {
            select: {
              id: true,
              name: true,
              image: true
            }
          },
          comments: {
            where: { rating: { not: null } },
            select: { rating: true }
          },
          _count: {
            select: {
              comments: true,
              versions: true
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.app.count({ where }),
      // Get available categories for filtering
      prisma.app.findMany({
        where: { status: 'APPROVED' },
        select: { category: true },
        distinct: ['category']
      })
    ])

    // Calculate ratings and format response
    type FormattedApp = {
      id: string
      name: string
      shortDescription: string | null
      description: string | null
      version: string
      category: string | null
      tags: string[]
      iconUrl: string | null
      screenshots: string[]
      downloadUrl: string | null
      website: string | null
      minVersion: string | null
      maxVersion: string | null
      fileSize: number | null
      downloads: number
      featured: boolean
      createdAt: Date
      updatedAt: Date
      developer: any
      stats: {
        totalComments: number
        totalVersions: number
        averageRating: number | null
        totalRatings: number
      }
    }

    const formattedApps: FormattedApp[] = apps.map((app: any) => {
      const ratings = app.comments.map((c: any) => c.rating).filter((r: any) => r !== null) as number[]
      const averageRating = ratings.length > 0
        ? Math.round((ratings.reduce((sum: number, rating: number) => sum + rating, 0) / ratings.length) * 10) / 10
        : null

      return {
        id: app.id,
        name: app.name,
        shortDescription: app.shortDescription,
        description: app.description,
        version: app.version,
        category: app.category,
        tags: app.tags,
        iconUrl: app.iconUrl,
        screenshots: app.screenshots,
        downloadUrl: app.downloadUrl,
        website: app.website,
        minVersion: app.minVersion,
        maxVersion: app.maxVersion,
        fileSize: app.fileSize,
        downloads: app.downloads,
        featured: app.featured,
        createdAt: app.createdAt,
        updatedAt: app.updatedAt,
        developer: app.developer,
        stats: {
          totalComments: app._count.comments,
          totalVersions: app._count.versions,
          averageRating,
          totalRatings: ratings.length
        }
      }
    })

    // Sort by rating if requested (now that we have calculated ratings)
    let sortedApps = formattedApps
    if (sortBy === 'rating') {
      sortedApps = formattedApps.sort((a: FormattedApp, b: FormattedApp) => {
        const aRating = a.stats.averageRating || 0
        const bRating = b.stats.averageRating || 0
        return sortOrder === 'desc' ? bRating - aRating : aRating - bRating
      })
    }

    // Prepare pagination info
    const totalPages = Math.ceil(total / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    const response = {
      success: true,
      apps: sortedApps,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      filters: {
        categories: categories.map((c: any) => c.category).filter((cat: string) => Boolean(cat)),
        availableSort: ['downloads', 'name', 'createdAt', 'rating']
      },
      timestamp: new Date().toISOString()
    }

    // Add CORS headers for external API usage
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
    }

    return NextResponse.json(response, { headers })

  } catch (error) {
    console.error('Error fetching apps list:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  })
}
