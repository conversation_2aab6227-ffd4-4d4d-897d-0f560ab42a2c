'use client'

import { Bell, Search, User, Settings, Menu, Shield, Users, Package } from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Avatar } from '@/components/ui/Avatar'
import { LanguageSelector } from '@/components/ui/LanguageSelector'
import { useTranslations } from 'next-intl'
import { useState, useEffect, useRef } from 'react'

interface HeaderProps {
  onMenuClick: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  const { user } = useAuth()
  const t = useTranslations()
  const [showUserMenu, setShowUserMenu] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside or pressing escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowUserMenu(false)
      }
    }

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [showUserMenu])

  return (
    <header className="h-16 bg-black border-b border-zinc-800/50 flex items-center justify-between px-4 sm:px-6 backdrop-blur-xl relative z-50">
      {/* Mobile menu button */}
      <button
        onClick={onMenuClick}
        className="lg:hidden p-2 text-zinc-400 hover:text-white transition-colors rounded-lg hover:bg-zinc-900/50"
      >
        <Menu className="w-6 h-6" />
      </button>

      {/* Search */}
      <div className="flex-1 max-w-lg mx-4 lg:mx-0">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-zinc-500" />
          <input
            type="text"
            placeholder={t('common.search') + '...'}
            className="w-full pl-10 pr-4 py-2.5 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 focus:bg-zinc-900/80 transition-all duration-200"
          />
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-4">
        {/* Language Selector */}
        <LanguageSelector variant="compact" />

        {/* Notifications */}
        <button className="relative p-2.5 text-zinc-400 hover:text-white transition-all duration-200 rounded-xl hover:bg-zinc-900/50 border border-transparent hover:border-zinc-800/50">
          <Bell className="w-5 h-5" />
          <span className="absolute top-1.5 right-1.5 w-2 h-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse"></span>
        </button>

        {/* User Menu */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center space-x-3 p-2 rounded-xl hover:bg-zinc-900/50 transition-all duration-200 border border-transparent hover:border-zinc-800/50"
          >
            <Avatar
              src={user?.image}
              name={user?.name}
              alt={user?.name || 'User'}
              size="md"
            />
            <span className="text-white text-sm font-medium">
              {user?.name}
            </span>
          </button>

          {/* Dropdown Menu */}
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-black/98 backdrop-blur-xl border border-zinc-700/80 rounded-xl shadow-2xl z-[99999] overflow-hidden">
              <div className="py-2">
                <Link
                  href="/profile"
                  className="flex items-center w-full px-4 py-3 text-sm text-zinc-300 hover:bg-zinc-800/80 hover:text-white transition-all duration-200"
                  onClick={() => setShowUserMenu(false)}
                >
                  <User className="w-4 h-4 mr-3 text-zinc-500" />
                  {t('common.profile')}
                </Link>
                <Link
                  href="/settings"
                  className="flex items-center w-full px-4 py-3 text-sm text-zinc-300 hover:bg-zinc-800/80 hover:text-white transition-all duration-200"
                  onClick={() => setShowUserMenu(false)}
                >
                  <Settings className="w-4 h-4 mr-3 text-zinc-500" />
                  {t('common.settings')}
                </Link>

                {/* Admin Section */}
                {user?.admin && (
                  <>
                    <div className="border-t border-zinc-700/50 my-2"></div>
                    <div className="px-4 py-2">
                      <p className="text-xs font-semibold text-zinc-500 uppercase tracking-wider">
                        Administration
                      </p>
                    </div>
                    <Link
                      href="/admin"
                      className="flex items-center w-full px-4 py-3 text-sm text-zinc-300 hover:bg-zinc-800/80 hover:text-white transition-all duration-200"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Shield className="w-4 h-4 mr-3 text-zinc-500" />
                      Admin Panel
                    </Link>
                    <Link
                      href="/admin/users"
                      className="flex items-center w-full px-4 py-3 text-sm text-zinc-300 hover:bg-zinc-800/80 hover:text-white transition-all duration-200"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Users className="w-4 h-4 mr-3 text-zinc-500" />
                      All Users
                    </Link>
                    <Link
                      href="/admin/reviews"
                      className="flex items-center w-full px-4 py-3 text-sm text-zinc-300 hover:bg-zinc-800/80 hover:text-white transition-all duration-200"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Package className="w-4 h-4 mr-3 text-zinc-500" />
                      App Reviews
                    </Link>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
