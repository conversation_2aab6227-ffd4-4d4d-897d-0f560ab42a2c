'use client'

import { useState, useEffect } from 'react'
import { X, Save, Loader2, Upload, Clock, Package } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface EditAppModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  appId: string
}

interface AppData {
  id: string
  name: string
  description: string
  shortDescription: string
  version: string
  category: string
  tags: string[]
  website?: string
  supportEmail?: string
  minVersion?: string
  maxVersion?: string
  changelog?: string
  iconUrl?: string
  screenshots?: string[]
  versions?: AppVersion[]
}

interface AppVersion {
  id: string
  version: string
  changelog?: string
  createdAt: string
}

interface FormErrors {
  [key: string]: string
}

const CATEGORIES = [
  'Productivity',
  'Entertainment',
  'Education',
  'Business',
  'Games',
  'Utilities',
  'Social',
  'Health & Fitness',
  'Finance',
  'Travel',
  'News',
  'Photo & Video',
  'Music',
  'Shopping',
  'Sports',
  'Weather',
  'Food & Drink',
  'Medical',
  'Navigation',
  'Reference'
]

export function EditAppModal({ isOpen, onClose, onSuccess, appId }: EditAppModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})
  const [formData, setFormData] = useState<AppData>({
    id: '',
    name: '',
    description: '',
    shortDescription: '',
    version: '',
    category: '',
    tags: [],
    website: '',
    supportEmail: '',
    minVersion: '',
    maxVersion: '',
    changelog: '',
    iconUrl: '',
    screenshots: [],
    versions: []
  })

  // File upload states
  const [appFile, setAppFile] = useState<File | null>(null)
  const [iconFile, setIconFile] = useState<File | null>(null)
  const [screenshotFiles, setScreenshotFiles] = useState<File[]>([])

  useEffect(() => {
    if (isOpen && appId) {
      fetchAppData()
    }
  }, [isOpen, appId])

  const fetchAppData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/app/${appId}`)

      if (response.ok) {
        const app = await response.json()
        setFormData({
          id: app.id,
          name: app.name,
          description: app.description,
          shortDescription: app.shortDescription || '',
          version: app.version,
          category: app.category,
          tags: app.tags || [],
          website: app.website || '',
          supportEmail: app.supportEmail || '',
          minVersion: app.minVersion || '',
          maxVersion: app.maxVersion || '',
          changelog: app.changelog || '',
          iconUrl: app.iconUrl || '',
          screenshots: app.screenshots || [],
          versions: app.versions || []
        })
      } else {
        console.error('Failed to fetch app data')
      }
    } catch (error) {
      console.error('Error fetching app data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (formData.tags.length === 0) {
      newErrors.tags = 'At least one tag is required'
    }

    if (!formData.shortDescription.trim()) {
      newErrors.shortDescription = 'Short description is required'
    } else if (formData.shortDescription.length > 150) {
      newErrors.shortDescription = 'Short description must be 150 characters or less'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    } else if (formData.description.length > 2000) {
      newErrors.description = 'Description must be 2000 characters or less'
    }

    if (!formData.category) {
      newErrors.category = 'Category is required'
    }

    if (!formData.version.match(/^\d+\.\d+\.\d+$/)) {
      newErrors.version = 'Version must be in format x.y.z (e.g., 1.0.0)'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const addTag = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim()) && formData.tags.length < 5) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const incrementVersion = () => {
    const versionParts = formData.version.split('.').map(Number)
    if (versionParts.length === 3 && versionParts.every(part => !isNaN(part))) {
      versionParts[2] += 1 // Increment patch version
      setFormData(prev => ({
        ...prev,
        version: versionParts.join('.')
      }))
    }
  }

  const handleFileUpload = async (): Promise<boolean> => {
    if (!appFile && !iconFile && screenshotFiles.length === 0) {
      return true // No files to upload
    }

    try {
      setIsUploading(true)
      const uploadFormData = new FormData()

      if (appFile) {
        uploadFormData.append('appFile', appFile)
      }

      if (iconFile) {
        uploadFormData.append('iconFile', iconFile)
      }

      screenshotFiles.forEach((file, index) => {
        uploadFormData.append('screenshots', file)
      })

      const response = await fetch(`/api/app/${appId}/upload`, {
        method: 'POST',
        body: uploadFormData
      })

      if (response.ok) {
        return true
      } else {
        const error = await response.json()
        console.error('Failed to upload files:', error.error)
        return false
      }
    } catch (error) {
      console.error('Error uploading files:', error)
      return false
    } finally {
      setIsUploading(false)
    }
  }

  const handleUpdate = async () => {
    if (!validateForm()) return

    try {
      setIsSaving(true)

      // First upload files if any
      const uploadSuccess = await handleFileUpload()
      if (!uploadSuccess) {
        return
      }

      // Then update app data
      const response = await fetch(`/api/app/${appId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          description: formData.description,
          shortDescription: formData.shortDescription,
          version: formData.version,
          category: formData.category,
          tags: formData.tags,
          websiteUrl: formData.website,
          supportEmail: formData.supportEmail,
          minVersion: formData.minVersion,
          maxVersion: formData.maxVersion,
          changelog: formData.changelog
        })
      })

      if (response.ok) {
        onSuccess()
        onClose()
      } else {
        const error = await response.json()
        console.error('Failed to update app:', error.error)
      }
    } catch (error) {
      console.error('Error updating app:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />

      <div className="relative w-full max-w-6xl max-h-[90vh] bg-black/95 backdrop-blur-xl border border-zinc-800/50 rounded-2xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-800/50">
          <div>
            <h2 className="text-2xl font-bold text-white">Update App</h2>
            <p className="text-zinc-400 mt-1">Update your app details and files</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-zinc-400 hover:text-white transition-colors rounded-lg hover:bg-zinc-800/50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-140px)]">
          {/* Main Form */}
          <div className="flex-1 p-6 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className="space-y-6">
                {/* App Name (Read-only) */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    App Name (Cannot be changed)
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    disabled
                    className="w-full px-4 py-3 bg-zinc-800/50 border border-zinc-700/50 rounded-xl text-zinc-400 cursor-not-allowed"
                  />
                </div>

                {/* Version with increment button */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Version <span className="text-red-400">*</span>
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={formData.version}
                      onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                      placeholder="1.0.0"
                      className="flex-1 px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                    />
                    <button
                      type="button"
                      onClick={incrementVersion}
                      className="px-4 py-3 bg-blue-600/20 border border-blue-500/30 text-blue-400 rounded-xl hover:bg-blue-600/30 transition-colors"
                      title="Increment version"
                    >
                      <Package className="w-4 h-4" />
                    </button>
                  </div>
                  {errors.version && <p className="text-red-400 text-sm mt-1">{errors.version}</p>}
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Tags <span className="text-red-400">*</span> (max 5)
                  </label>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 bg-blue-600/20 text-blue-400 border border-blue-500/30 rounded-full text-sm"
                      >
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-2 text-blue-400 hover:text-blue-300"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <input
                    type="text"
                    placeholder="Type a tag and press Enter"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addTag(e.currentTarget.value)
                        e.currentTarget.value = ''
                      }
                    }}
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  />
                  {errors.tags && <p className="text-red-400 text-sm mt-1">{errors.tags}</p>}
                </div>

                {/* Short Description */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Short Description <span className="text-red-400">*</span> ({formData.shortDescription.length}/150)
                  </label>
                  <textarea
                    value={formData.shortDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, shortDescription: e.target.value }))}
                    placeholder="Brief description of your app"
                    rows={3}
                    maxLength={150}
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 resize-none"
                  />
                  {errors.shortDescription && <p className="text-red-400 text-sm mt-1">{errors.shortDescription}</p>}
                </div>

                {/* Main Description */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Main Description <span className="text-red-400">*</span> ({formData.description.length}/2000)
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Detailed description of your app, its features, and functionality"
                    rows={6}
                    maxLength={2000}
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 resize-none"
                  />
                  {errors.description && <p className="text-red-400 text-sm mt-1">{errors.description}</p>}
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Category <span className="text-red-400">*</span>
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  >
                    <option value="">Select a category</option>
                    {CATEGORIES.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  {errors.category && <p className="text-red-400 text-sm mt-1">{errors.category}</p>}
                </div>

                {/* Changelog */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Changelog for this update
                  </label>
                  <textarea
                    value={formData.changelog}
                    onChange={(e) => setFormData(prev => ({ ...prev, changelog: e.target.value }))}
                    placeholder="Describe what's new in this version..."
                    rows={4}
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 resize-none"
                  />
                </div>

                {/* File Uploads */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Update Files</h3>
                  
                  {/* App File */}
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      App File (.zip)
                    </label>
                    <input
                      type="file"
                      accept=".zip"
                      onChange={(e) => setAppFile(e.target.files?.[0] || null)}
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                    />
                  </div>

                  {/* Icon File */}
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      App Icon (.png, .jpg)
                    </label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => setIconFile(e.target.files?.[0] || null)}
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                    />
                  </div>

                  {/* Screenshots */}
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      Screenshots (max 5)
                    </label>
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={(e) => setScreenshotFiles(Array.from(e.target.files || []).slice(0, 5))}
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                    />
                  </div>
                </div>

                {/* Optional Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Website URL</label>
                    <input
                      type="url"
                      value={formData.website}
                      onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                      placeholder="https://yourapp.com"
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Support Email</label>
                    <input
                      type="email"
                      value={formData.supportEmail}
                      onChange={(e) => setFormData(prev => ({ ...prev, supportEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                    />
                  </div>
                </div>

                {/* System Version Requirements */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Minimum System Version</label>
                    <input
                      type="text"
                      value={formData.minVersion}
                      onChange={(e) => setFormData(prev => ({ ...prev, minVersion: e.target.value }))}
                      placeholder="e.g., 10.0"
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Maximum System Version</label>
                    <input
                      type="text"
                      value={formData.maxVersion}
                      onChange={(e) => setFormData(prev => ({ ...prev, maxVersion: e.target.value }))}
                      placeholder="e.g., 15.0"
                      className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Updates Sidebar */}
          <div className="w-80 border-l border-zinc-800/50 bg-zinc-900/30 p-6 overflow-y-auto">
            <div className="flex items-center space-x-2 mb-4">
              <Clock className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Updates</h3>
            </div>
            
            {formData.versions && formData.versions.length > 0 ? (
              <div className="space-y-4">
                {formData.versions
                  .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                  .map((version) => (
                    <div key={version.id} className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-blue-400 font-medium">v{version.version}</span>
                        <span className="text-zinc-500 text-xs">{formatDate(version.createdAt)}</span>
                      </div>
                      {version.changelog && (
                        <p className="text-zinc-300 text-sm">{version.changelog}</p>
                      )}
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Clock className="w-12 h-12 text-zinc-600 mx-auto mb-3" />
                <p className="text-zinc-500">No version history yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-4 p-6 border-t border-zinc-800/50 bg-zinc-900/30">
          <button
            onClick={onClose}
            className="px-6 py-2 text-zinc-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleUpdate}
            disabled={isSaving || isLoading || isUploading}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-colors"
          >
            {(isSaving || isUploading) ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>
              {isUploading ? 'Uploading...' : isSaving ? 'Updating...' : 'Update'}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}
