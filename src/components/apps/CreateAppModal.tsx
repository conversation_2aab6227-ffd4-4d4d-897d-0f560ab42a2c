'use client'

import { useState, useRef } from 'react'
import { X, ChevronLeft, ChevronRight, Check, Upload, Image, FileText, Package } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface CreateAppModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface AppFormData {
  name: string
  acceptTerms: boolean
  tags: string[]
  shortDescription: string
  description: string
  category: string
  version: string
  icon: File | null
  appFile: File | null
  screenshots: File[]
  websiteUrl: string
  supportEmail: string
  minVersion: string
  maxVersion: string
}

const CATEGORIES = [
  'Games',
  'Productivity',
  'Utilities',
  'Social',
  'Entertainment',
  'Education',
  'Business',
  'Developer Tools',
  'Graphics & Design',
  'Music & Audio',
  'Video & Photo',
  'Health & Fitness',
  'Travel',
  'News',
  'Finance',
  'Other'
]

const STEPS = [
  { id: 1, title: 'App Name & Agreement', description: 'Basic information and terms' },
  { id: 2, title: 'App Basics', description: 'Details and categorization' },
  { id: 3, title: 'Files & Media', description: 'Upload app files and images' },
  { id: 4, title: 'Additional Setup', description: 'Optional configuration' },
  { id: 5, title: 'Creating App', description: 'Processing your submission' }
]

export function CreateAppModal({ isOpen, onClose, onSuccess }: CreateAppModalProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitProgress, setSubmitProgress] = useState(0)
  const [submitStatus, setSubmitStatus] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const iconInputRef = useRef<HTMLInputElement>(null)
  const screenshotInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState<AppFormData>({
    name: '',
    acceptTerms: false,
    tags: [],
    shortDescription: '',
    description: '',
    category: '',
    version: '1.0.0',
    icon: null,
    appFile: null,
    screenshots: [],
    websiteUrl: '',
    supportEmail: '',
    minVersion: '',
    maxVersion: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  if (!isOpen) return null

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!formData.name.trim()) {
          newErrors.name = 'App name is required'
        } else if (formData.name.length < 3 || formData.name.length > 50) {
          newErrors.name = 'App name must be between 3 and 50 characters'
        }
        if (!formData.acceptTerms) {
          newErrors.acceptTerms = 'You must accept the terms to continue'
        }
        break

      case 2:
        if (formData.tags.length === 0) {
          newErrors.tags = 'At least one tag is required'
        }
        if (!formData.shortDescription.trim()) {
          newErrors.shortDescription = 'Short description is required'
        } else if (formData.shortDescription.length > 150) {
          newErrors.shortDescription = 'Short description must be 150 characters or less'
        }
        if (!formData.description.trim()) {
          newErrors.description = 'Description is required'
        } else if (formData.description.length > 2000) {
          newErrors.description = 'Description must be 2000 characters or less'
        }
        if (!formData.category) {
          newErrors.category = 'Category is required'
        }
        if (!formData.version.match(/^\d+\.\d+\.\d+$/)) {
          newErrors.version = 'Version must be in format x.y.z (e.g., 1.0.0)'
        }
        break

      case 3:
        if (!formData.icon) {
          newErrors.icon = 'App icon is required'
        }
        if (!formData.appFile) {
          newErrors.appFile = 'App file is required'
        }
        if (formData.screenshots.length < 2) {
          newErrors.screenshots = 'At least 2 screenshots are required'
        }
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < 5) {
        setCurrentStep(currentStep + 1)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSkipStep4 = () => {
    setCurrentStep(5)
  }

  const addTag = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim()) && formData.tags.length < 5) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const validateAppFileType = (file: File): { isValid: boolean; error?: string } => {
    const fileName = file.name.toLowerCase()
    const fileExtension = fileName.split('.').pop()

    // Define allowed file types
    const allowedTypes = {
      windows: ['.exe'],
      android: ['.apk'],
      ios: ['.ipa'],
      archives: ['.zip', '.rar', '.7z']
    }

    const allAllowedExtensions = [
      ...allowedTypes.windows,
      ...allowedTypes.android,
      ...allowedTypes.ios,
      ...allowedTypes.archives
    ]

    if (!fileExtension || !allAllowedExtensions.includes(`.${fileExtension}`)) {
      return {
        isValid: false,
        error: `Invalid file type. Only the following file types are allowed:
        • Windows: .exe files
        • Android: .apk files
        • iOS: .ipa files
        • Archives: .zip, .rar, .7z files`
      }
    }

    return { isValid: true }
  }

  const handleFileUpload = (type: 'icon' | 'app' | 'screenshots', files: FileList | null) => {
    if (!files) return

    switch (type) {
      case 'icon':
        const iconFile = files[0]
        if (iconFile && iconFile.size <= 2 * 1024 * 1024) { // 2MB limit
          setFormData(prev => ({ ...prev, icon: iconFile }))
          setErrors(prev => ({ ...prev, icon: '' }))
        } else {
          setErrors(prev => ({ ...prev, icon: 'Icon must be 2MB or smaller' }))
        }
        break

      case 'app':
        const appFile = files[0]
        if (!appFile) return

        // Validate file type first
        const typeValidation = validateAppFileType(appFile)
        if (!typeValidation.isValid) {
          setErrors(prev => ({ ...prev, appFile: typeValidation.error || 'Invalid file type' }))
          return
        }

        // Validate file size
        if (appFile.size <= 150 * 1024 * 1024) { // 150MB limit
          setFormData(prev => ({ ...prev, appFile }))
          setErrors(prev => ({ ...prev, appFile: '' }))
        } else {
          setErrors(prev => ({ ...prev, appFile: 'App file must be 150MB or smaller' }))
        }
        break

      case 'screenshots':
        const screenshots = Array.from(files).slice(0, 6) // Max 6 screenshots
        const validScreenshots = screenshots.filter(file => file.size <= 5 * 1024 * 1024) // 5MB each
        setFormData(prev => ({ ...prev, screenshots: validScreenshots }))
        if (validScreenshots.length !== screenshots.length) {
          setErrors(prev => ({ ...prev, screenshots: 'Some screenshots were too large (max 5MB each)' }))
        } else {
          setErrors(prev => ({ ...prev, screenshots: '' }))
        }
        break
    }
  }

  const submitAppCreation = async () => {
    setIsSubmitting(true)
    setSubmitProgress(0)

    try {
      setSubmitStatus('Setting up app metadata...')
      setSubmitProgress(20)

      // Create FormData
      const formDataToSubmit = new FormData()
      formDataToSubmit.append('name', formData.name)
      formDataToSubmit.append('tags', JSON.stringify(formData.tags))
      formDataToSubmit.append('shortDescription', formData.shortDescription)
      formDataToSubmit.append('description', formData.description)
      formDataToSubmit.append('category', formData.category)
      formDataToSubmit.append('version', formData.version)
      formDataToSubmit.append('websiteUrl', formData.websiteUrl)
      formDataToSubmit.append('supportEmail', formData.supportEmail)
      formDataToSubmit.append('minVersion', formData.minVersion)
      formDataToSubmit.append('maxVersion', formData.maxVersion)

      setSubmitStatus('Uploading app files...')
      setSubmitProgress(40)

      // Add files
      if (formData.icon) {
        formDataToSubmit.append('icon', formData.icon)
      }
      if (formData.appFile) {
        formDataToSubmit.append('appFile', formData.appFile)
      }

      setSubmitStatus('Processing screenshots...')
      setSubmitProgress(60)

      // Add screenshots
      formData.screenshots.forEach((screenshot) => {
        formDataToSubmit.append('screenshots', screenshot)
      })

      setSubmitStatus('Saving to database...')
      setSubmitProgress(80)

      // Submit to API
      const response = await fetch('/api/app/create', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_API_KEY}`
        },
        body: formDataToSubmit
      })

      setSubmitProgress(100)

      if (response.ok) {
        await response.json()
        setSubmitStatus('App created successfully!')
        await new Promise(resolve => setTimeout(resolve, 1000))
        onSuccess()
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create app')
      }
    } catch (error) {
      console.error('Error creating app:', error)
      setSubmitStatus('Error creating app. Please try again.')
      setSubmitProgress(0)
      await new Promise(resolve => setTimeout(resolve, 2000))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = async () => {
    if (currentStep === 4) {
      setCurrentStep(5)
    }

    if (currentStep === 5) {
      await submitAppCreation()
    }
  }

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />

      <div className="relative w-full max-w-4xl max-h-[90vh] bg-black/95 backdrop-blur-xl border border-zinc-800/50 rounded-2xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-800/50">
          <div>
            <h2 className="text-2xl font-bold text-white">Create New App</h2>
            <p className="text-zinc-400 mt-1">{STEPS[currentStep - 1]?.description}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-zinc-400 hover:text-white transition-colors rounded-lg hover:bg-zinc-800/50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-between p-6 bg-zinc-900/30">
          {STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                currentStep > step.id
                  ? 'bg-emerald-500 text-white'
                  : currentStep === step.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-zinc-700 text-zinc-400'
              }`}>
                {currentStep > step.id ? <Check className="w-4 h-4" /> : step.id}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                currentStep >= step.id ? 'text-white' : 'text-zinc-400'
              }`}>
                {step.title}
              </span>
              {index < STEPS.length - 1 && (
                <div className={`w-8 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-emerald-500' : 'bg-zinc-700'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[50vh]">
          {/* Step 1: App Name & Agreement */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  App Name <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter your app name (3-50 characters)"
                  className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                />
                {errors.name && <p className="text-red-400 text-sm mt-1">{errors.name}</p>}
              </div>

              <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={(e) => setFormData(prev => ({ ...prev, acceptTerms: e.target.checked }))}
                    className="mt-1 w-4 h-4 text-blue-600 bg-zinc-800 border-zinc-600 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <label htmlFor="acceptTerms" className="text-sm text-zinc-300">
                    <span className="text-red-400">*</span> By creating this app, you confirm that you accept our{' '}
                    <a href="/terms" className="text-blue-400 hover:text-blue-300 underline">Terms of Service</a>{' '}
                    and{' '}
                    <a href="/developer-agreement" className="text-blue-400 hover:text-blue-300 underline">Developer Agreement</a>
                  </label>
                </div>
                {errors.acceptTerms && <p className="text-red-400 text-sm mt-2">{errors.acceptTerms}</p>}
              </div>
            </div>
          )}

          {/* Step 2: App Basics */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Tags <span className="text-red-400">*</span> (max 5)
                </label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {formData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 bg-blue-600/20 text-blue-400 border border-blue-500/30 rounded-full text-sm"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="ml-2 text-blue-400 hover:text-blue-300"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
                <input
                  type="text"
                  placeholder="Type a tag and press Enter"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      addTag(e.currentTarget.value)
                      e.currentTarget.value = ''
                    }
                  }}
                  className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                />
                {errors.tags && <p className="text-red-400 text-sm mt-1">{errors.tags}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Short Description <span className="text-red-400">*</span> ({formData.shortDescription.length}/150)
                </label>
                <textarea
                  value={formData.shortDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, shortDescription: e.target.value }))}
                  placeholder="Brief description of your app"
                  rows={3}
                  maxLength={150}
                  className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 resize-none"
                />
                {errors.shortDescription && <p className="text-red-400 text-sm mt-1">{errors.shortDescription}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Main Description <span className="text-red-400">*</span> ({formData.description.length}/2000)
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Detailed description of your app, its features, and functionality"
                  rows={6}
                  maxLength={2000}
                  className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 resize-none"
                />
                {errors.description && <p className="text-red-400 text-sm mt-1">{errors.description}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Category <span className="text-red-400">*</span>
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  >
                    <option value="">Select a category</option>
                    {CATEGORIES.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  {errors.category && <p className="text-red-400 text-sm mt-1">{errors.category}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Version <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.version}
                    onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                    placeholder="1.0.0"
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  />
                  {errors.version && <p className="text-red-400 text-sm mt-1">{errors.version}</p>}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Files & Media */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  App Icon <span className="text-red-400">*</span> (max 2MB, 512x512px recommended)
                </label>
                <div className="border-2 border-dashed border-zinc-700 rounded-xl p-6 text-center">
                  {formData.icon ? (
                    <div className="flex items-center justify-center space-x-4">
                      <img
                        src={URL.createObjectURL(formData.icon)}
                        alt="App icon preview"
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="text-left">
                        <p className="text-white font-medium">{formData.icon.name}</p>
                        <p className="text-zinc-400 text-sm">{(formData.icon.size / 1024 / 1024).toFixed(2)} MB</p>
                      </div>
                      <button
                        onClick={() => setFormData(prev => ({ ...prev, icon: null }))}
                        className="text-red-400 hover:text-red-300"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  ) : (
                    <div>
                      <Image className="w-12 h-12 text-zinc-400 mx-auto mb-4" />
                      <p className="text-zinc-400 mb-2">Click to upload app icon</p>
                      <button
                        onClick={() => iconInputRef.current?.click()}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        Choose File
                      </button>
                    </div>
                  )}
                </div>
                <input
                  ref={iconInputRef}
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileUpload('icon', e.target.files)}
                  className="hidden"
                />
                {errors.icon && <p className="text-red-400 text-sm mt-1">{errors.icon}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Main App File <span className="text-red-400">*</span> (max 150MB, .exe/.apk/.ipa/.zip/.rar/.7z)
                </label>
                <div className="border-2 border-dashed border-zinc-700 rounded-xl p-6 text-center">
                  {formData.appFile ? (
                    <div className="flex items-center justify-center space-x-4">
                      <FileText className="w-12 h-12 text-blue-400" />
                      <div className="text-left">
                        <p className="text-white font-medium">{formData.appFile.name}</p>
                        <p className="text-zinc-400 text-sm">{(formData.appFile.size / 1024 / 1024).toFixed(2)} MB</p>
                      </div>
                      <button
                        onClick={() => setFormData(prev => ({ ...prev, appFile: null }))}
                        className="text-red-400 hover:text-red-300"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  ) : (
                    <div>
                      <Upload className="w-12 h-12 text-zinc-400 mx-auto mb-4" />
                      <p className="text-zinc-400 mb-2">Click to upload your app file</p>
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        Choose File
                      </button>
                    </div>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".exe,.apk,.ipa,.zip,.rar,.7z"
                  onChange={(e) => handleFileUpload('app', e.target.files)}
                  className="hidden"
                />
                {errors.appFile && <p className="text-red-400 text-sm mt-1">{errors.appFile}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Screenshots <span className="text-red-400">*</span> (min 2, max 6, 5MB each)
                </label>
                <div className="border-2 border-dashed border-zinc-700 rounded-xl p-6">
                  {formData.screenshots.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                      {formData.screenshots.map((screenshot, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(screenshot)}
                            alt={`Screenshot ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                          />
                          <button
                            onClick={() => {
                              const newScreenshots = formData.screenshots.filter((_, i) => i !== index)
                              setFormData(prev => ({ ...prev, screenshots: newScreenshots }))
                            }}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : null}

                  <div className="text-center">
                    <Image className="w-12 h-12 text-zinc-400 mx-auto mb-4" />
                    <p className="text-zinc-400 mb-2">Add screenshots of your app</p>
                    <button
                      onClick={() => screenshotInputRef.current?.click()}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      Add Screenshots
                    </button>
                  </div>
                </div>
                <input
                  ref={screenshotInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleFileUpload('screenshots', e.target.files)}
                  className="hidden"
                />
                {errors.screenshots && <p className="text-red-400 text-sm mt-1">{errors.screenshots}</p>}
              </div>
            </div>
          )}

          {/* Step 4: Additional Setup */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-lg font-semibold text-white mb-2">Optional Configuration</h3>
                <p className="text-zinc-400">These settings can be configured later if needed</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Website URL</label>
                  <input
                    type="url"
                    value={formData.websiteUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, websiteUrl: e.target.value }))}
                    placeholder="https://yourapp.com"
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">Support Email</label>
                  <input
                    type="email"
                    value={formData.supportEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, supportEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Minimum System Version</label>
                  <input
                    type="text"
                    value={formData.minVersion}
                    onChange={(e) => setFormData(prev => ({ ...prev, minVersion: e.target.value }))}
                    placeholder="e.g., 10.0"
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">Maximum System Version</label>
                  <input
                    type="text"
                    value={formData.maxVersion}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxVersion: e.target.value }))}
                    placeholder="e.g., 15.0"
                    className="w-full px-4 py-3 bg-zinc-900/50 border border-zinc-800/50 rounded-xl text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Creating App */}
          {currentStep === 5 && (
            <div className="text-center py-8">
              {!isSubmitting ? (
                <div>
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Package className="w-12 h-12 text-blue-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Ready to Create Your App</h3>
                  <p className="text-zinc-400 mb-8">
                    Click the button below to start creating your app. This process may take a few moments.
                  </p>
                  <button
                    onClick={handleSubmit}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Create App Now
                  </button>
                </div>
              ) : (
                <div>
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <LoadingSpinner size="lg" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Creating Your App</h3>
                  <p className="text-zinc-400 mb-6">{submitStatus}</p>

                  <div className="w-full max-w-md mx-auto">
                    <div className="bg-zinc-800 rounded-full h-2 mb-4">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${submitProgress}%` }}
                      />
                    </div>
                    <p className="text-sm text-zinc-400">{Math.round(submitProgress)}% complete</p>
                  </div>

                  {submitProgress === 100 && (
                    <div className="mt-6">
                      <div className="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Check className="w-8 h-8 text-white" />
                      </div>
                      <p className="text-emerald-400 font-medium">App created successfully!</p>
                      <p className="text-zinc-400 text-sm mt-2">Your app is now pending review and will be available once approved.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-zinc-800/50 bg-zinc-900/30">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1 || isSubmitting}
            className="flex items-center space-x-2 px-4 py-2 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>

          <div className="flex items-center space-x-3">
            {currentStep === 4 && (
              <button
                onClick={handleSkipStep4}
                className="px-4 py-2 text-zinc-400 hover:text-white transition-colors"
              >
                Skip this step
              </button>
            )}

            {currentStep < 4 ? (
              <button
                onClick={handleNext}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                <span>Next</span>
                <ChevronRight className="w-4 h-4" />
              </button>
            ) : currentStep === 4 ? (
              <button
                onClick={handleSubmit}
                className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                <span>Create App</span>
              </button>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
}
