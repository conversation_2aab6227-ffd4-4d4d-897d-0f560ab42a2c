'use client'

import { useState, useEffect, useRef } from 'react'
import { Send, Bot, User, Loader2, Co<PERSON>, ThumbsUp, ThumbsDown } from 'lucide-react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  suggestions?: string[]
}

export function ChatInterface() {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    // Add welcome message
    setMessages([
      {
        id: '1',
        role: 'assistant',
        content: `Hello! I'm **<PERSON>na AI**, your intelligent assistant for the AveHub Developer Portal. I can help you with:

• **App Development** - Best practices, guidelines, and recommendations
• **Platform Statistics** - Current metrics, trends, and insights
• **Approval Process** - Understanding the review workflow
• **Troubleshooting** - Solving common issues and problems
• **General Support** - Any questions about the platform

## Quick Examples

Here are some things you can ask me:

\`\`\`
"Show me platform statistics"
"How do I improve my app approval chances?"
"What file formats are supported?"
\`\`\`

**How can I assist you today?**`,
        timestamp: new Date(),
        suggestions: [
          'Show me platform statistics',
          'How do I improve my app?',
          'Explain the approval process',
          'Show me a code example'
        ]
      }
    ])

    // Listen for quick action events
    const handleQuickAction = (event: CustomEvent) => {
      if (event.detail?.prompt) {
        setInput(event.detail.prompt)
        inputRef.current?.focus()
      }
    }

    window.addEventListener('avena-quick-action', handleQuickAction as EventListener)
    return () => {
      window.removeEventListener('avena-quick-action', handleQuickAction as EventListener)
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(msg => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp.toISOString()
          }))
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to get AI response')
      }

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response.message,
        timestamp: new Date(),
        suggestions: data.response.suggestions
      }

      setMessages(prev => [...prev, assistantMessage])

    } catch (error) {
      console.error('Chat error:', error)
      setError(error instanceof Error ? error.message : 'Failed to send message')

      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request. Please try again or contact support if the issue persists.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  const useSuggestion = (suggestion: string) => {
    setInput(suggestion)
    inputRef.current?.focus()
  }

  return (
    <div className="bg-zinc-900/30 border border-zinc-800/50 rounded-xl flex flex-col h-[600px]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-zinc-800/50">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-white">Avena AI</h3>
            <p className="text-xs text-zinc-400">Powered by Google Gemini 2.0 Flash</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex space-x-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {/* Avatar */}
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${
                message.role === 'user'
                  ? 'bg-blue-600'
                  : 'bg-gradient-to-br from-purple-600 to-blue-600'
              }`}>
                {message.role === 'user' ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>

              {/* Message Content */}
              <div className={`rounded-xl p-4 ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-zinc-800/50 text-zinc-100 border border-zinc-700/50'
              }`}>
                {message.role === 'user' ? (
                  // User messages - simple text rendering
                  <div className="prose prose-sm prose-invert max-w-none">
                    {message.content.split('\n').map((line, index) => (
                      <div key={index} className={line ? 'mb-1' : 'mb-2'}>
                        {line || ''}
                      </div>
                    ))}
                  </div>
                ) : (
                  // Assistant messages - markdown rendering
                  <MarkdownRenderer
                    content={message.content}
                    className="prose prose-sm prose-invert max-w-none"
                  />
                )}

                {/* Message Actions */}
                {message.role === 'assistant' && (
                  <div className="flex items-center justify-between mt-3 pt-3 border-t border-zinc-700/50">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => copyMessage(message.content)}
                        className="p-1 text-zinc-400 hover:text-zinc-300 transition-colors"
                        title="Copy message"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-zinc-400 hover:text-green-400 transition-colors" title="Good response">
                        <ThumbsUp className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-zinc-400 hover:text-red-400 transition-colors" title="Poor response">
                        <ThumbsDown className="w-4 h-4" />
                      </button>
                    </div>
                    <span className="text-xs text-zinc-500">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                )}

                {/* Suggestions */}
                {message.suggestions && message.suggestions.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-zinc-700/50">
                    <p className="text-xs text-zinc-400 mb-2">Suggested questions:</p>
                    <div className="space-y-1">
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => useSuggestion(suggestion)}
                          className="block w-full text-left text-xs text-zinc-300 hover:text-white bg-zinc-700/30 hover:bg-zinc-700/50 rounded px-2 py-1 transition-colors"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex space-x-3 max-w-[80%]">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div className="bg-zinc-800/50 border border-zinc-700/50 rounded-xl p-4">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin text-zinc-400" />
                  <span className="text-zinc-400 text-sm">Thinking...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-zinc-800/50">
        {error && (
          <div className="mb-3 p-2 bg-red-900/20 border border-red-800/50 rounded-lg text-red-400 text-sm">
            {error}
          </div>
        )}

        <div className="flex space-x-3">
          <textarea
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask Avena AI anything about the platform..."
            className="flex-1 bg-zinc-800/50 border border-zinc-700/50 rounded-xl px-4 py-3 text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 resize-none"
            rows={1}
            disabled={isLoading}
          />
          <button
            onClick={sendMessage}
            disabled={!input.trim() || isLoading}
            className="px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-zinc-700 disabled:cursor-not-allowed text-white rounded-xl transition-colors flex items-center justify-center"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
