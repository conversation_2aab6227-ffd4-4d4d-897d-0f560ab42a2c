# AveHub Developer Portal - App Store API

> A comprehensive REST API for app store integration and application management

## 🚀 Quick Start

### Base URL
```
https://your-domain.com/api
```

### Authentication
All API endpoints require authentication using an API key:

**Environment Variable:**
```bash
API_KEY=your-secure-api-key-here
```

**Request Headers:**
```http
Authorization: Bearer your-secure-api-key-here
```
**OR**
```http
X-API-Key: your-secure-api-key-here
```

---

## 📱 App Store API Endpoints

### 1. List All Apps
Retrieve paginated list of approved applications for app store display.

**Endpoint:** `GET /api/app/list`

ggg

**Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `category` (optional): Filter by category
- `search` (optional): Search in name/description/tags
- `featured` (optional): Filter featured apps (`true`/`false`)
- `sortBy` (optional): Sort field (`downloads`, `name`, `createdAt`, `rating`)
- `sortOrder` (optional): Sort direction (`asc`, `desc`)

**Example Request:**
```bash
curl -X GET "https://your-domain.com/api/app/list?page=1&limit=20&featured=true" \
  -H "Authorization: Bearer your-api-key"
```

**Response:**
```json
{
  "success": true,
  "apps": [
    {
      "id": "app-id",
      "name": "My Awesome App",
      "shortDescription": "Brief app description",
      "description": "Full app description",
      "version": "1.0.0",
      "category": "productivity",
      "tags": ["productivity", "tools"],
      "iconUrl": "/api/files/icons/app-icon.png",
      "screenshots": ["/api/files/screenshots/shot1.png"],
      "downloadUrl": "/api/files/downloads/app.zip",
      "website": "https://myapp.com",
      "supportEmail": "<EMAIL>",
      "minVersion": "10.0",
      "maxVersion": "15.0",
      "fileSize": 1048576,
      "downloads": 1234,
      "featured": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "developer": {
        "id": "dev-id",
        "name": "Developer Name",
        "email": "<EMAIL>",
        "image": "/api/files/avatars/dev.png"
      },
      "stats": {
        "totalComments": 15,
        "totalVersions": 3,
        "averageRating": 4.5,
        "totalRatings": 10
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "filters": {
    "categories": ["productivity", "games", "utilities"],
    "availableSort": ["downloads", "name", "createdAt", "rating"]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

### 2. Get App Details
Retrieve comprehensive information about a specific application.

**Endpoint:** `GET /api/app/info/{id}`

**Parameters:**
- `id` (required): App ID

**Example Request:**
```bash
curl -X GET "https://your-domain.com/api/app/info/app-id" \
  -H "Authorization: Bearer your-api-key"
```

**Response:**
```json
{
  "success": true,
  "app": {
    "id": "app-id",
    "name": "My Awesome App",
    "description": "Detailed app description with features...",
    "shortDescription": "Brief description",
    "version": "1.0.0",
    "category": "productivity",
    "tags": ["productivity", "tools", "workflow"],
    "iconUrl": "/api/files/icons/app-icon.png",
    "screenshots": [
      "/api/files/screenshots/shot1.png",
      "/api/files/screenshots/shot2.png"
    ],
    "downloadUrl": "/api/files/downloads/app.zip",
    "website": "https://myapp.com",
    "supportEmail": "<EMAIL>",
    "minVersion": "10.0",
    "maxVersion": "15.0",
    "fileSize": 1048576,
    "downloads": 1234,
    "status": "APPROVED",
    "featured": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "developer": {
      "id": "dev-id",
      "name": "Developer Name",
      "email": "<EMAIL>",
      "image": "/api/files/avatars/dev.png"
    },
    "stats": {
      "totalComments": 15,
      "totalVersions": 3,
      "averageRating": 4.5,
      "totalRatings": 10
    },
    "latestComments": [
      {
        "id": "comment-id",
        "content": "Great app!",
        "rating": 5,
        "user": {
          "id": "user-id",
          "name": "User Name",
          "image": "/api/files/avatars/user.png"
        },
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "versionHistory": [
      {
        "id": "version-id",
        "version": "1.0.0",
        "changelog": "Initial release",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

### 3. Download App
Initiate app download and track download statistics.

**Endpoint:** `GET /api/app/download/{id}`

**Parameters:**
- `id` (required): App ID

**Example Request:**
```bash
curl -X GET "https://your-domain.com/api/app/download/app-id" \
  -H "Authorization: Bearer your-api-key"
```

**Response:**
```json
{
  "success": true,
  "downloadUrl": "/api/files/downloads/app.zip",
  "filename": "MyAwesomeApp-v1.0.0.zip",
  "fileSize": 1048576,
  "version": "1.0.0",
  "checksum": "sha256-hash",
  "expiresAt": "2024-01-01T01:00:00Z",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

### 4. Get User Apps (Developer Dashboard)
Retrieve apps belonging to the authenticated user.

**Endpoint:** `GET /api/user/apps`

**Authentication:** API Key + Session Required

**Example Request:**
```bash
curl -X GET "https://your-domain.com/api/user/apps" \
  -H "Authorization: Bearer your-api-key" \
  -H "Cookie: next-auth.session-token=session-token"
```

**Response:**
```json
{
  "apps": [
    {
      "id": "app-id",
      "name": "My App",
      "status": "APPROVED",
      "downloads": 1234,
      "version": "1.0.0",
      "iconUrl": "/api/files/icons/app-icon.png",
      "shortDescription": "Brief description",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

---

## 📁 File Serving API

All uploaded files (icons, screenshots, downloads) are served through our internal file serving endpoint:

**Endpoint:** `GET /api/files/{...path}`

**Example URLs:**
- Icons: `/api/files/icons/app-icon.png`
- Screenshots: `/api/files/screenshots/shot1.png`
- Downloads: `/api/files/downloads/app.zip`
- Avatars: `/api/files/avatars/user.png`

**Features:**
- Automatic content-type detection
- Cache headers for optimization
- Support for images, executables, and archives
- Proper error handling for missing files

---

## 🔧 API Configuration

### Environment Variables
```bash
# Required
API_KEY=your-secure-api-key-here
DATABASE_URL=your-database-connection-string
NEXTAUTH_SECRET=your-nextauth-secret

# Storage Configuration
NETLIFY_SITE_ID=your-netlify-site-id
NETLIFY_TOKEN=your-netlify-token

# Optional
NEXTAUTH_URL=https://your-domain.com
```

### Error Responses
All endpoints return standardized error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Common Error Codes:**
- `MISSING_API_KEY` (401): API key not provided
- `INVALID_API_KEY` (401): API key is invalid
- `APP_NOT_FOUND` (404): App does not exist
- `APP_NOT_AVAILABLE` (403): App not approved for public access
- `INTERNAL_ERROR` (500): Server error

### Rate Limiting
- **App List:** 100 requests per minute
- **App Info:** 200 requests per minute  
- **Download:** 50 requests per minute per IP
- **User Apps:** 30 requests per minute per user

---

## 🚀 Integration Examples

### JavaScript/TypeScript
```typescript
const API_KEY = 'your-api-key';
const BASE_URL = 'https://your-domain.com/api';

async function getApps(page = 1, limit = 20) {
  const response = await fetch(
    `${BASE_URL}/app/list?page=${page}&limit=${limit}`,
    {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
}

async function downloadApp(appId: string) {
  const response = await fetch(
    `${BASE_URL}/app/download/${appId}`,
    {
      headers: {
        'Authorization': `Bearer ${API_KEY}`
      }
    }
  );
  
  const data = await response.json();
  
  if (data.success) {
    window.open(data.downloadUrl, '_blank');
  }
}
```

### Python
```python
import requests

API_KEY = 'your-api-key'
BASE_URL = 'https://your-domain.com/api'

def get_apps(page=1, limit=20, category=None):
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    params = {'page': page, 'limit': limit}
    if category:
        params['category'] = category
    
    response = requests.get(
        f'{BASE_URL}/app/list',
        headers=headers,
        params=params
    )
    
    response.raise_for_status()
    return response.json()

def get_app_info(app_id):
    headers = {'Authorization': f'Bearer {API_KEY}'}
    
    response = requests.get(
        f'{BASE_URL}/app/info/{app_id}',
        headers=headers
    )
    
    response.raise_for_status()
    return response.json()
```

---

## 📝 Changelog

### v1.0.0 (Current)
- ✅ App listing with pagination and filtering
- ✅ Detailed app information retrieval
- ✅ Download tracking and file serving
- ✅ Developer dashboard API
- ✅ File serving with proper MIME types
- ✅ API key authentication
- ✅ CORS support for external integration

---

## 📞 Support

For API support and integration assistance:
- 📧 Email: <EMAIL>
- 📖 Documentation: Full API docs available in dashboard
- 🐛 Issues: Report bugs through developer portal

---

**Note:** This API is designed for external app store integration. All file URLs use internal serving endpoints (`/api/files/`) for security and consistency across environments.
