import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  /* config options here */
  serverExternalPackages: ['@prisma/client'],
  // Increase the maximum request body size to 100MB
  async headers() {
    return []
  },
  // Configure body size limits
  serverRuntimeConfig: {
    maxUploadSize: 100 * 1024 * 1024, // 100MB
  },
};

export default withNextIntl(nextConfig);
